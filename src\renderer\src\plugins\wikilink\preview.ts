import { Plugin, PluginKey } from '@milkdown/kit/prose/state'
import { EditorView } from '@milkdown/kit/prose/view'
import { Crepe } from '@milkdown/crepe'
import { replaceAll } from '@milkdown/kit/utils'
import type { WikiLinkConfig } from './index'
import { createPreviewEditor, destroyPreviewEditor } from '../../components/features/PreviewEditor'

/**
 * 预览插件的状态
 */
interface PreviewState {
  target: string | null
  visible: boolean
  x: number
  y: number
}

/**
 * 嵌套预览管理器 - 统一管理多层级预览窗口
 */
export class NestedPreviewManager {
  private static instance: NestedPreviewManager | null = null
  private previewStack: PreviewInstance[] = []
  private maxNestingLevel = 5 // 最大嵌套层级
  private baseZIndex = 1000
  private globalMouseHandler: ((event: MouseEvent) => void) | null = null
  private keydownHandler: ((event: KeyboardEvent) => void) | null = null
  private hideTimeout: NodeJS.Timeout | null = null
  private smartHideTimeout: NodeJS.Timeout | null = null // 智能层级隐藏计时器
  private lastSmartHideLevel = 0 // 上次智能隐藏的层级，用于防抖
  private lastTriggerTime = 0 // 上次触发时间，用于防抖
  private lastNestedHideTime = 0 // 上次嵌套隐藏时间，用于防抖

  // 状态跟踪，用于减少重复日志
  private lastMouseState = { onPreview: false, onWikiLink: false, inMainEditor: false, previewCount: 0, previewLevel: 0 }
  private lastLogTime = 0

  // 主编辑器预览管理器引用
  private mainPreviewManager: PreviewTooltipView | null = null

  // 性能优化：内容缓存和预览实例池
  private contentCache = new Map<string, { content: string, timestamp: number }>()
  private readonly CACHE_DURATION = 5 * 60 * 1000 // 5分钟缓存
  private previewPool = new Map<string, PreviewInstance>() // 预览实例池，按目标名称索引

  static getInstance(): NestedPreviewManager {
    if (!NestedPreviewManager.instance) {
      NestedPreviewManager.instance = new NestedPreviewManager()
    }
    return NestedPreviewManager.instance
  }

  constructor() {
    console.log(`🏗️ [管理器] 初始化`)
    this.setupGlobalMouseTracking()
  }

  /**
   * 创建新的预览实例 - 增强的存在性检查和状态管理
   */
  createPreview(target: string, x: number, y: number, config: WikiLinkConfig, parentLevel = 0): PreviewInstance | null {
    console.log(`🏗️ [NestedPreviewManager] 创建预览请求: ${target}, 父级层级: ${parentLevel}`)
    console.log(`🏗️ [NestedPreviewManager] 当前预览栈:`, this.previewStack.map(p => `${p.target}(L${p.level})`))

    // 检查嵌套层级限制
    if (parentLevel >= this.maxNestingLevel) {
      console.warn(`WikiLink 预览嵌套层级已达到最大值 ${this.maxNestingLevel}`)
      return null
    }

    const level = parentLevel + 1

    // 增强的预览存在性检查
    const existing = this.findExistingPreview(target, level)
    if (existing) {
      console.log(`♻️ [NestedPreviewManager] 复用现有预览: ${target} (Level ${level})`)

      // 取消任何隐藏计时器
      this.cancelAllHideTimers()

      // 重新计算智能位置
      const smartPosition = this.calculateSmartPosition(x, y, level)
      existing.updatePosition(smartPosition.x, smartPosition.y)

      // 确保预览可见且状态正确
      existing.ensureVisible()

      console.log(`✅ [NestedPreviewManager] 现有预览已复用并更新`)
      return existing
    }

    const zIndex = this.baseZIndex + level * 10

    // 计算智能位置
    const smartPosition = this.calculateSmartPosition(x, y, level)
    console.log(`🏗️ [NestedPreviewManager] 智能位置计算: 原始(${x}, ${y}) → 智能(${smartPosition.x}, ${smartPosition.y})`)

    console.log(`🏗️ [NestedPreviewManager] 创建新预览实例: ${target}, 层级: ${level}, z-index: ${zIndex}`)
    const preview = new PreviewInstance(target, level, zIndex, config)
    this.previewStack.push(preview)

    console.log(`🏗️ [NestedPreviewManager] 开始显示预览`)
    preview.show(smartPosition.x, smartPosition.y)
    console.log(`🏗️ [NestedPreviewManager] 预览创建完成，当前栈大小: ${this.previewStack.length}`)
    return preview
  }

  /**
   * 移除预览实例
   */
  removePreview(instance: PreviewInstance) {
    const index = this.previewStack.indexOf(instance)
    if (index > -1) {
      // 移除当前实例及其所有子级预览
      const childPreviews = this.previewStack.filter(p => p.level > instance.level)
      childPreviews.forEach(child => child.destroy())

      this.previewStack = this.previewStack.filter(p => p.level <= instance.level && p !== instance)
      instance.destroy()
    }
  }

  /**
   * 清理所有预览
   */
  clearAll() {
    this.previewStack.forEach(preview => preview.destroy())
    this.previewStack = []
  }

  /**
   * 获取当前最高层级
   */
  getCurrentMaxLevel(): number {
    return this.previewStack.length > 0 ? Math.max(...this.previewStack.map(p => p.level)) : 0
  }

  /**
   * 设置全局鼠标跟踪 - 优化的层级化预览窗口交互逻辑
   */
  private setupGlobalMouseTracking() {
    this.cleanupGlobalMouseTracking()

    this.globalMouseHandler = (event: MouseEvent) => {
      const target = event.target as HTMLElement

      // 安全检查：确保 target 是有效的 DOM 元素
      if (!target || typeof target.closest !== 'function' || typeof target.hasAttribute !== 'function') {
        return
      }

      // 增强的预览窗口检测 - 包括预览窗口的边界区域
      const previewElement = target.closest('.wikilink-preview-tooltip')
      const isOnAnyPreview = !!previewElement

      // 增强的 WikiLink 检测 - 更精确的识别
      const wikiLinkElement = target.hasAttribute('data-wikilink') ? target : target.closest('[data-wikilink]')
      const isOnWikiLink = !!wikiLinkElement

      // 检查鼠标是否在主编辑器区域内
      const isInMainEditor = !!target.closest('.milkdown') && !isOnAnyPreview

      // 获取当前悬浮的预览层级（用于优化交互）
      const currentPreviewLevel = previewElement ? parseInt(previewElement.className.match(/level-(\d+)/)?.[1] || '0') : 0

      const currentState = {
        onPreview: isOnAnyPreview,
        onWikiLink: isOnWikiLink,
        inMainEditor: isInMainEditor,
        previewCount: this.previewStack.length,
        previewLevel: currentPreviewLevel
      }

      // 智能日志记录 - 减少噪音，只记录重要状态变化
      const now = Date.now()
      const stateChanged =
        currentState.onPreview !== this.lastMouseState.onPreview ||
        currentState.onWikiLink !== this.lastMouseState.onWikiLink ||
        currentState.previewCount !== this.lastMouseState.previewCount ||
        currentState.previewLevel !== this.lastMouseState.previewLevel

      const shouldLog = stateChanged || (now - this.lastLogTime > 30000) // 增加到30秒，进一步减少噪音

      if (shouldLog) {
        // console.log(`🔍 [优化鼠标] 预览=${currentState.onPreview}(L${currentState.previewLevel}), WikiLink=${currentState.onWikiLink}, 主编辑器=${currentState.inMainEditor}, 数量=${currentState.previewCount}`)
        this.lastMouseState = currentState
        this.lastLogTime = now
      }

      // 优化的隐藏逻辑：更智能的预览管理
      if (!isOnAnyPreview && !isOnWikiLink) {
        // 鼠标完全离开了预览和 WikiLink 区域
        // if (shouldLog) console.log(`🔄 [优化鼠标] 开始延迟隐藏所有预览`)
        this.scheduleHideAll()
      } else {
        // 鼠标在预览或 WikiLink 上，取消隐藏
        // if (shouldLog && this.hideTimeout) console.log(`✅ [优化鼠标] 取消隐藏，保持预览显示`)
        this.cancelHideAll()

        // 只有在鼠标在 WikiLink 上时才取消智能隐藏计时器
        if (isOnWikiLink) {
          if (shouldLog) console.log(`🔄 [鼠标跟踪] 鼠标在 WikiLink 上，取消智能隐藏计时器`)
          this.cancelAllHideTimers()
        }

        // 简单的嵌套预览隐藏：只有在鼠标在低层级预览上且不在 WikiLink 上时才隐藏高层级预览
        if (isOnAnyPreview && !isOnWikiLink && currentPreviewLevel > 0) {
          // 鼠标在预览窗口的非 WikiLink 区域，检查是否需要隐藏更高层级的预览
          if (shouldLog) console.log(`🎯 [鼠标跟踪] 鼠标在 Level ${currentPreviewLevel} 非 WikiLink 区域，准备隐藏更高层级`)
          this.scheduleSimpleNestedHide(currentPreviewLevel)
        }
      }
    }

    document.addEventListener('mousemove', this.globalMouseHandler)

    // ESC 键隐藏所有预览 - 增强的键盘交互
    this.keydownHandler = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        console.log(`⌨️ [ESC键] 用户请求关闭所有预览`)
        this.clearAll()
        // 清理缓存以释放内存
        this.cleanupExpiredCache()
      }
    }
    document.addEventListener('keydown', this.keydownHandler)

    // 定期清理过期缓存
    setInterval(() => {
      this.cleanupExpiredCache()
    }, 60000) // 每分钟清理一次
  }

  /**
   * 清理全局鼠标跟踪
   */
  private cleanupGlobalMouseTracking() {
    if (this.globalMouseHandler) {
      document.removeEventListener('mousemove', this.globalMouseHandler)
      this.globalMouseHandler = null
    }
    if (this.keydownHandler) {
      document.removeEventListener('keydown', this.keydownHandler)
      this.keydownHandler = null
    }
    if (this.smartHideTimeout) {
      clearTimeout(this.smartHideTimeout)
      this.smartHideTimeout = null
    }
  }

  /**
   * 层级化隐藏逻辑 - 从最高层级开始逐层隐藏
   */
  private scheduleLayeredHide() {
    // 清除之前的隐藏计时器
    if (this.hideTimeout) {
      clearTimeout(this.hideTimeout)
      this.hideTimeout = null
    }

    console.log(`⏰ [层级隐藏] 设置300ms延迟隐藏`)
    this.hideTimeout = setTimeout(() => {
      this.performLayeredHide()
    }, 300)
  }

  /**
   * 隐藏所有预览 - 包括主编辑器预览和嵌套预览
   */
  private scheduleHideAll() {
    // 减少日志噪音，只在没有计时器时记录
    const shouldLog = !this.hideTimeout

    // 清除之前的隐藏计时器
    if (this.hideTimeout) {
      clearTimeout(this.hideTimeout)
      this.hideTimeout = null
    }

    this.hideTimeout = setTimeout(() => {
      // 隐藏主编辑器预览
      this.hideMainPreview()
      // 隐藏所有嵌套预览
      this.clearAll()
      // 清除计时器引用
      this.hideTimeout = null
    }, 300)
  }

  /**
   * 执行层级化隐藏
   */
  private performLayeredHide() {
    if (this.previewStack.length === 0) return

    // 按层级从高到低排序
    const sortedPreviews = [...this.previewStack].sort((a, b) => b.level - a.level)

    // 找到最高层级
    const maxLevel = sortedPreviews[0].level

    // 只隐藏最高层级的预览窗口
    const topLevelPreviews = sortedPreviews.filter(p => p.level === maxLevel)

    console.log(`🔄 层级化隐藏：隐藏第 ${maxLevel} 层预览窗口 (${topLevelPreviews.length} 个)`)

    topLevelPreviews.forEach(preview => {
      this.removePreview(preview)
    })

    // 如果还有其他层级的预览窗口，继续延迟隐藏
    if (this.previewStack.length > 0) {
      console.log(`🔄 还有 ${this.previewStack.length} 个预览窗口，继续延迟隐藏`)
      this.scheduleLayeredHide()
    }
  }

  /**
   * 取消隐藏所有预览 - 公共方法
   */
  public cancelHideAll() {
    if (this.hideTimeout) {
      console.log(`❌ [层级隐藏] 取消延迟隐藏`)
      clearTimeout(this.hideTimeout)
      this.hideTimeout = null
    }
  }

  /**
   * 检查是否有活跃的预览窗口
   */
  public hasActivePreview(target: string): boolean {
    // 检查主编辑器预览
    if (this.mainPreviewManager && this.mainPreviewManager.getCurrentPreviewTarget() === target) {
      return true
    }

    // 检查嵌套预览栈
    return this.previewStack.some(preview => preview.target === target)
  }

  /**
   * 更新预览位置
   */
  public updatePreviewPosition(target: string, x: number, y: number) {
    // 更新主编辑器预览位置
    if (this.mainPreviewManager && this.mainPreviewManager.getCurrentPreviewTarget() === target) {
      this.mainPreviewManager.updateCurrentPreviewPosition(x, y)
      return
    }

    // 更新嵌套预览位置
    const preview = this.previewStack.find(p => p.target === target)
    if (preview) {
      preview.updatePosition(x, y)
    }
  }

  /**
   * 外部触发层级化隐藏检查 - 供嵌套预览插件调用，带防抖机制
   */
  triggerLayeredHideCheck(currentLevel?: number) {
    // 防抖：避免频繁调用
    const now = Date.now()
    if (this.lastTriggerTime && now - this.lastTriggerTime < 100) {
      return // 100ms 内的重复调用直接忽略
    }
    this.lastTriggerTime = now

    if (currentLevel) {
      console.log(`🔄 [NestedPreviewManager] 触发层级化隐藏检查，当前层级: ${currentLevel}`)
      // 智能层级隐藏：只有在鼠标真正离开高层级预览时才隐藏
      // 这里不立即隐藏，而是设置一个短暂的延迟，让用户有机会移动到更高层级
      this.scheduleSmartLayeredHide(currentLevel)
    } else {
      console.log(`🔄 [NestedPreviewManager] 外部触发层级化隐藏检查`)
      // 模拟鼠标不在任何预览窗口或 WikiLink 上的情况
      this.scheduleLayeredHide()
    }
  }

  /**
   * 简单的嵌套预览隐藏 - 只在鼠标真正离开时隐藏
   */
  private scheduleSimpleNestedHide(currentLevel: number) {
    // 防抖：避免频繁调用
    const now = Date.now()
    if (this.lastNestedHideTime && now - this.lastNestedHideTime < 200) {
      return // 200ms 内的重复调用直接忽略
    }
    this.lastNestedHideTime = now

    // 清除之前的智能隐藏计时器
    if (this.smartHideTimeout) {
      clearTimeout(this.smartHideTimeout)
      this.smartHideTimeout = null
    }

    // 设置延迟隐藏更高层级的预览
    this.smartHideTimeout = setTimeout(() => {
      const maxLevel = this.getCurrentMaxLevel()
      if (maxLevel > currentLevel) {
        console.log(`🎯 [简单嵌套隐藏] 隐藏 Level ${currentLevel} 以上的 ${maxLevel - currentLevel} 个预览`)
        this.hidePreviewsAboveLevel(currentLevel)
      }
      this.smartHideTimeout = null
    }, 500) // 较长的延迟，给用户时间移动鼠标
  }

  /**
   * 智能层级化隐藏 - 给用户时间移动到更高层级，带防抖机制
   */
  private scheduleSmartLayeredHide(currentLevel: number) {
    // 防抖：如果已经有相同层级的智能隐藏在等待，不重复设置
    if (this.smartHideTimeout && this.lastSmartHideLevel === currentLevel) {
      console.log(`🔄 [智能隐藏] 防抖：Level ${currentLevel} 已在等待隐藏`)
      return
    }

    // 清除之前的智能隐藏计时器
    if (this.smartHideTimeout) {
      clearTimeout(this.smartHideTimeout)
    }

    this.lastSmartHideLevel = currentLevel

    // 设置合适的延迟（500ms），给用户充足时间移动鼠标到更高层级预览
    this.smartHideTimeout = setTimeout(() => {
      // 再次检查鼠标位置，如果仍在当前层级或更低，则隐藏更高层级
      const maxLevel = this.getCurrentMaxLevel()
      if (maxLevel > currentLevel) {
        console.log(`🎯 [智能层级隐藏] 延迟后隐藏 Level ${currentLevel} 以上的预览`)
        this.hidePreviewsAboveLevel(currentLevel)
      }
      this.smartHideTimeout = null
      this.lastSmartHideLevel = 0
    }, 500)
  }

  /**
   * 隐藏指定层级以上的预览窗口
   */
  private hidePreviewsAboveLevel(level: number) {
    const previewsToHide = this.previewStack.filter(p => p.level > level)
    if (previewsToHide.length > 0) {
      console.log(`🎯 [层级隐藏] 隐藏 Level ${level} 以上的 ${previewsToHide.length} 个预览`)
      previewsToHide.forEach(preview => {
        this.removePreview(preview)
      })
    }
  }

  /**
   * 查找现有的预览实例
   */
  private findExistingPreview(target: string, level: number): PreviewInstance | null {
    // 首先检查预览栈中是否有匹配的预览
    const stackPreview = this.previewStack.find(p => p.target === target && p.level === level && !p.getIsDestroyed())
    if (stackPreview) {
      return stackPreview
    }

    // 检查 DOM 中是否还有对应的预览容器
    const container = document.querySelector(`.wikilink-preview-tooltip.level-${level}`)
    if (container) {
      // 如果 DOM 中有容器但预览栈中没有，说明状态不同步，需要清理
      console.warn(`⚠️ [状态同步] 发现孤立的预览容器 Level ${level}，正在清理`)
      container.remove()
    }

    return null
  }

  /**
   * 取消所有隐藏计时器
   */
  private cancelAllHideTimers() {
    if (this.hideTimeout) {
      clearTimeout(this.hideTimeout)
      this.hideTimeout = null
      console.log(`❌ [计时器] 取消全局隐藏计时器`)
    }

    if (this.smartHideTimeout) {
      clearTimeout(this.smartHideTimeout)
      this.smartHideTimeout = null
      console.log(`❌ [计时器] 取消智能隐藏计时器`)
    }
  }

  /**
   * 性能优化：获取缓存的内容
   */
  private getCachedContent(target: string): string | null {
    const cached = this.contentCache.get(target)
    if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
      console.log(`📋 [缓存命中] 使用缓存内容: ${target}`)
      return cached.content
    }
    if (cached) {
      console.log(`📋 [缓存过期] 清除过期缓存: ${target}`)
      this.contentCache.delete(target)
    }
    return null
  }

  /**
   * 性能优化：缓存内容
   */
  private setCachedContent(target: string, content: string) {
    this.contentCache.set(target, {
      content,
      timestamp: Date.now()
    })
    console.log(`📋 [缓存存储] 缓存内容: ${target} (${content.length} 字符)`)
  }

  /**
   * 性能优化：清理过期缓存
   */
  private cleanupExpiredCache() {
    const now = Date.now()
    let cleanedCount = 0
    for (const [target, cached] of this.contentCache.entries()) {
      if (now - cached.timestamp >= this.CACHE_DURATION) {
        this.contentCache.delete(target)
        cleanedCount++
      }
    }
    if (cleanedCount > 0) {
      console.log(`🧹 [缓存清理] 清理了 ${cleanedCount} 个过期缓存项`)
    }
  }

  /**
   * 性能优化：预览实例复用
   */
  private getPooledPreview(target: string, level: number): PreviewInstance | null {
    const poolKey = `${target}_L${level}`
    const pooled = this.previewPool.get(poolKey)
    if (pooled && !pooled.getIsDestroyed()) {
      console.log(`♻️ [实例复用] 复用预览实例: ${target} (Level ${level})`)
      return pooled
    }
    if (pooled) {
      this.previewPool.delete(poolKey)
    }
    return null
  }

  /**
   * 性能优化：将预览实例放入池中
   */
  private poolPreview(preview: PreviewInstance) {
    const poolKey = `${preview.target}_L${preview.level}`
    this.previewPool.set(poolKey, preview)
    console.log(`♻️ [实例池] 预览实例已入池: ${preview.target} (Level ${preview.level})`)
  }

  /**
   * 注册主编辑器预览管理器
   */
  registerMainPreviewManager(manager: PreviewTooltipView) {
    this.mainPreviewManager = manager
    // console.log(`📝 [NestedPreviewManager] 注册主编辑器预览管理器`)
  }

  /**
   * 隐藏主编辑器预览
   */
  hideMainPreview() {
    if (this.mainPreviewManager) {
      this.mainPreviewManager.hidePreview()
    }
  }

  /**
   * 响应式位置计算 - 基于 WikiLink 位置的四角策略，支持不同屏幕尺寸
   */
  private calculateSmartPosition(originalX: number, originalY: number, level: number): { x: number, y: number } {
    // 响应式尺寸计算
    const viewportWidth = window.innerWidth
    const viewportHeight = window.innerHeight

    // 根据屏幕大小调整预览窗口尺寸
    let PREVIEW_WIDTH = 500
    let PREVIEW_HEIGHT = 400

    // 小屏幕适配
    if (viewportWidth < 768) {
      PREVIEW_WIDTH = Math.min(400, viewportWidth - 40)
      PREVIEW_HEIGHT = Math.min(300, viewportHeight - 100)
    } else if (viewportWidth < 1024) {
      PREVIEW_WIDTH = Math.min(450, viewportWidth - 60)
      PREVIEW_HEIGHT = Math.min(350, viewportHeight - 120)
    }

    // 优化的间距设置：既贴近又不遮挡
    const CLOSE_GAP = 8 // 与 WikiLink 的间距，确保不遮挡且容易移动鼠标
    const MARGIN = Math.max(10, Math.min(20, viewportWidth * 0.02)) // 响应式边距

    console.log(`📐 [贴近定位] Level ${level} 屏幕: ${viewportWidth}x${viewportHeight}, 预览: ${PREVIEW_WIDTH}x${PREVIEW_HEIGHT}`)
    console.log(`📐 [贴近定位] WikiLink 坐标: 中心(${originalX}), 底部+5px(${originalY})`)

    // 右下角优先策略，考虑到 originalY 已经是 WikiLink 底部 + 5px
    // originalX 是 WikiLink 中心，originalY 是 WikiLink 底部 + 5px
    const strategies = [
      // 1. 右下角 (最优) - 预览窗口显示在 WikiLink 右下方
      {
        name: '右下',
        x: originalX + CLOSE_GAP,
        y: originalY // 直接使用，因为已经有 5px 间距
      },
      // 2. 左下角 - 预览窗口显示在 WikiLink 左下方
      {
        name: '左下',
        x: originalX - PREVIEW_WIDTH - CLOSE_GAP,
        y: originalY // 直接使用，因为已经有 5px 间距
      },
      // 3. 右上角 - 预览窗口显示在 WikiLink 右上方
      {
        name: '右上',
        x: originalX + CLOSE_GAP,
        y: originalY - PREVIEW_HEIGHT - 10 // 需要额外间距避免遮挡
      },
      // 4. 左上角 - 预览窗口显示在 WikiLink 左上方
      {
        name: '左上',
        x: originalX - PREVIEW_WIDTH - CLOSE_GAP,
        y: originalY - PREVIEW_HEIGHT - 10 // 需要额外间距避免遮挡
      }
    ]

    // 选择第一个不超出边界的位置
    for (const strategy of strategies) {
      if (this.isPositionValid(strategy.x, strategy.y, PREVIEW_WIDTH, PREVIEW_HEIGHT, viewportWidth, viewportHeight, MARGIN)) {
        console.log(`📐 [响应式定位] Level ${level} 选择"${strategy.name}"位置: (${strategy.x}, ${strategy.y})`)
        return { x: strategy.x, y: strategy.y }
      }
    }

    // 如果都不可行，使用安全的中心位置
    const safeX = Math.max(MARGIN, Math.min(viewportWidth - PREVIEW_WIDTH - MARGIN, originalX))
    const safeY = Math.max(MARGIN, Math.min(viewportHeight - PREVIEW_HEIGHT - MARGIN, originalY))
    console.log(`📐 [响应式定位] Level ${level} 使用安全位置: (${safeX}, ${safeY})`)
    return { x: safeX, y: safeY }
  }



  /**
   * 检查位置是否有效 (不超出视窗边界)
   */
  private isPositionValid(x: number, y: number, width: number, height: number, viewportWidth: number, viewportHeight: number, margin: number): boolean {
    return x >= margin &&
           y >= margin &&
           x + width <= viewportWidth - margin &&
           y + height <= viewportHeight - margin
  }
}

/**
 * 预览实例 - 每个预览窗口的独立编辑器实例
 */
class PreviewInstance {
  public readonly target: string
  public readonly level: number
  public readonly zIndex: number
  private config: WikiLinkConfig
  private container: HTMLElement | null = null
  private editorContainer: HTMLElement | null = null
  private editor: Crepe | null = null
  private isLoading = false
  private isDestroyed = false
  private isEditing = false
  private originalContent = ''
  private saveTimer: NodeJS.Timeout | null = null

  constructor(target: string, level: number, zIndex: number, config: WikiLinkConfig) {
    this.target = target
    this.level = level
    this.zIndex = zIndex
    this.config = config
  }

  /**
   * 显示预览窗口
   */
  async show(x: number, y: number) {
    if (this.isDestroyed) return

    this.createContainer(x, y)
    this.showLoadingState()

    try {
      const content = await this.loadContent()
      if (this.isDestroyed) return // 检查是否在加载过程中被销毁

      await this.createEditor(content)
      // 鼠标跟踪由全局管理器统一处理，不需要单独设置
    } catch (error) {
      console.error('预览加载失败:', error)
      this.showErrorState(error)
    }
  }

  /**
   * 更新位置
   */
  updatePosition(x: number, y: number) {
    if (this.container) {
      this.positionContainer(x, y)
    }
  }

  /**
   * 获取容器位置信息 - 供智能定位算法使用
   */
  getContainerRect(): DOMRect | null {
    return this.container ? this.container.getBoundingClientRect() : null
  }

  /**
   * 检查是否已销毁 - 供性能优化使用
   */
  public getIsDestroyed(): boolean {
    return this.isDestroyed
  }

  /**
   * 确保预览可见且状态正确
   */
  public ensureVisible() {
    if (this.container) {
      // 确保容器可见
      this.container.style.display = 'block'
      this.container.style.visibility = 'visible'
      this.container.style.opacity = '1'

      // 如果正在显示加载状态，但编辑器已经创建，则隐藏加载状态
      if (this.editor && this.container.querySelector('.loading-indicator')) {
        const loadingElement = this.container.querySelector('.loading-indicator')
        if (loadingElement) {
          loadingElement.remove()
        }
      }

      console.log(`✅ [PreviewInstance] 预览已确保可见 (层级: ${this.level}, 目标: ${this.target})`)
    }
  }



  /**
   * 刷新预览内容 - 智能刷新，避免不必要的加载状态
   */
  async refreshContent() {
    if (this.isDestroyed || this.isLoading) return

    console.log(`🔄 刷新预览内容 (层级: ${this.level}, 目标: ${this.target})`)
    console.log(`📋 当前内容长度: ${this.originalContent.length}`)

    try {
      this.isLoading = true

      // 如果已经有编辑器，不显示加载状态，直接在后台刷新
      const hasEditor = !!this.editor
      if (!hasEditor) {
        this.showLoadingState()
      }

      // 重新加载内容
      const content = await this.loadContent()
      if (this.isDestroyed) return

      console.log(`📋 新加载内容长度: ${content.length}`)
      console.log(`📋 内容是否相同: ${content === this.originalContent}`)

      // 如果内容有变化，重新创建编辑器
      if (content !== this.originalContent) {
        console.log(`📝 内容已更新，重新创建编辑器 (层级: ${this.level}, 目标: ${this.target})`)

        // 销毁旧编辑器
        if (this.editor) {
          destroyPreviewEditor(this.editor)
          this.editor = null
        }

        // 创建新编辑器
        await this.createEditor(content)
      } else {
        console.log(`✅ 内容无变化 (层级: ${this.level}, 目标: ${this.target})`)
        // 如果内容无变化且已有编辑器，确保编辑器可见
        if (hasEditor && this.editor) {
          this.ensureVisible()
        }
      }
    } catch (error) {
      console.error('刷新预览内容失败:', error)
      this.showErrorState(error)
    } finally {
      this.isLoading = false
    }
  }

  /**
   * 销毁预览实例
   */
  destroy() {
    if (this.isDestroyed) return
    this.isDestroyed = true

    if (this.saveTimer) {
      clearTimeout(this.saveTimer)
      this.saveTimer = null
    }

    if (this.editor) {
      destroyPreviewEditor(this.editor)
      this.editor = null
    }

    if (this.container) {
      this.container.remove()
      this.container = null
    }

    this.editorContainer = null
  }

  /**
   * 创建容器
   */
  private createContainer(x: number, y: number) {
    this.container = document.createElement('div')
    this.container.className = `wikilink-preview-tooltip level-${this.level}`
    this.container.style.cssText = `
      position: fixed;
      z-index: ${this.zIndex};
      background: white;
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      box-shadow: 0 10px 25px rgba(0,0,0,0.15);
      max-width: 500px;
      max-height: 400px;
      overflow: hidden;
      pointer-events: auto;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `

    // 保持简洁统一的外观，不添加复杂的层级样式

    this.positionContainer(x, y)
    document.body.appendChild(this.container)
  }

  /**
   * 定位容器
   */
  private positionContainer(x: number, y: number) {
    if (!this.container) return

    const viewportWidth = window.innerWidth
    const viewportHeight = window.innerHeight
    const containerWidth = 500
    const containerHeight = 400

    // 智能定位，避免超出视窗
    let left = x + 10
    let top = y + 10

    // 水平位置调整
    if (left + containerWidth > viewportWidth) {
      left = x - containerWidth - 10
    }
    if (left < 10) {
      left = 10
    }

    // 垂直位置调整
    if (top + containerHeight > viewportHeight) {
      top = y - containerHeight - 10
    }
    if (top < 10) {
      top = 10
    }

    // 嵌套层级偏移
    const offset = (this.level - 1) * 20
    left += offset
    top += offset

    this.container.style.left = `${left}px`
    this.container.style.top = `${top}px`
  }

  /**
   * 显示优化的加载状态
   */
  private showLoadingState() {
    if (!this.container) return

    // 添加层级信息和更好的视觉效果
    const levelText = this.level > 1 ? ` (Level ${this.level})` : ''

    this.container.innerHTML = `
      <div style="
        padding: 24px;
        text-align: center;
        color: #6b7280;
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        border-radius: 8px;
        min-height: 120px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
      ">
        <div style="
          display: inline-block;
          width: 24px;
          height: 24px;
          border: 3px solid #e2e8f0;
          border-top: 3px solid #3b82f6;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          margin-bottom: 12px;
        "></div>
        <div style="font-size: 14px; font-weight: 500; color: #475569;">
          正在加载 "${this.target}"${levelText}
        </div>
        <div style="font-size: 12px; color: #94a3b8; margin-top: 4px;">
          请稍候...
        </div>
      </div>
      <style>
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      </style>
    `
  }

  /**
   * 显示错误状态
   */
  private showErrorState(error: any) {
    if (!this.container) return

    this.container.innerHTML = `
      <div style="padding: 20px; text-align: center; color: #ef4444;">
        <div style="font-size: 16px; margin-bottom: 8px;">⚠️</div>
        <div style="font-size: 14px;">加载失败</div>
        <div style="font-size: 12px; color: #6b7280; margin-top: 4px;">${error.message || '未知错误'}</div>
      </div>
    `
  }

  /**
   * 加载内容
   */
  private async loadContent(): Promise<string> {
    if (!this.config.readPageContent) {
      throw new Error('未配置内容读取函数')
    }

    try {
      const content = await this.config.readPageContent(this.target)
      return content || `# ${this.target}\n\n页面内容为空`
    } catch (error) {
      console.error('加载页面内容失败:', error)
      throw new Error(`无法加载页面 "${this.target}": ${error.message || '未知错误'}`)
    }
  }

  /**
   * 创建编辑器实例 - 使用编辑器工厂复用主编辑器配置
   */
  private async createEditor(content: string) {
    if (!this.container || this.isDestroyed) return

    // 创建编辑器容器
    this.editorContainer = document.createElement('div')
    this.editorContainer.style.cssText = `
      padding: 16px;
      min-height: 200px;
      max-height: 350px;
      overflow-y: auto;
    `

    this.container.innerHTML = ''
    this.container.appendChild(this.editorContainer)

    try {
      console.log(`🔄 开始创建预览编辑器 (层级: ${this.level}, 目标: ${this.target})`)

      // 使用编辑器工厂函数，复用主编辑器配置
      this.editor = await createPreviewEditor(
        this.editorContainer,
        content,
        this.config,
        this.level
      )

      // 检查是否在创建过程中被销毁
      if (this.isDestroyed) {
        console.log(`⚠️ 预览在创建过程中被销毁 (层级: ${this.level}, 目标: ${this.target})`)
        if (this.editor) {
          this.editor.destroy()
          this.editor = null
        }
        return
      }

      // 保存原始内容
      this.originalContent = content

      // 添加编辑功能
      this.setupEditMode()

      console.log(`✅ 预览编辑器创建成功 (层级: ${this.level}, 目标: ${this.target})`)

    } catch (error) {
      console.error(`❌ 创建预览编辑器失败 (层级: ${this.level}, 目标: ${this.target}):`, error)
      this.showErrorState(error)
    }
  }



  /**
   * 设置编辑模式
   */
  private setupEditMode() {
    if (!this.editorContainer) return

    // 添加点击事件进入编辑模式
    this.editorContainer.addEventListener('click', () => {
      if (!this.isEditing) {
        this.enterEditMode()
      }
    })
  }

  /**
   * 进入编辑模式 - 使用 Crepe 原生 API
   */
  private enterEditMode() {
    if (!this.editor || this.isEditing) return

    this.isEditing = true

    // 使用 Crepe 原生 API 启用编辑模式
    this.editor.setReadonly(false)

    // 设置自动保存
    this.setupAutoSave()

    console.log(`📝 进入编辑模式 (层级: ${this.level}, 目标: ${this.target})`)
  }

  /**
   * 退出编辑模式 - 使用 Crepe 原生 API
   */
  private exitEditMode() {
    if (!this.editor || !this.isEditing) return

    this.isEditing = false

    // 保存当前内容
    const currentContent = this.editor.getMarkdown()
    if (currentContent !== this.originalContent) {
      this.saveContent(currentContent)
    }

    // 使用 Crepe 原生 API 禁用编辑模式
    this.editor.setReadonly(true)

    console.log(`💾 退出编辑模式 (层级: ${this.level}, 目标: ${this.target})`)
  }

  /**
   * 设置自动保存 - 使用 Crepe 原生事件监听
   */
  private setupAutoSave() {
    if (!this.editor) return

    // 使用 Crepe 原生事件监听内容变化
    this.editor.on((listener) => {
      listener.markdownUpdated((ctx, markdown) => {
        // 清除之前的定时器
        if (this.saveTimer) {
          clearTimeout(this.saveTimer)
        }

        // 设置新的保存定时器
        this.saveTimer = setTimeout(() => {
          if (markdown !== this.originalContent) {
            this.originalContent = markdown
            this.saveContentSilently(markdown)
          }
        }, 1000)
      })
    })
  }

  /**
   * 保存内容 - 使用 Crepe 原生 API
   */
  private async saveContent(markdown: string) {
    try {
      if (this.config.savePageContent && markdown !== this.originalContent) {
        await this.config.savePageContent(this.target, markdown)
        this.originalContent = markdown
        console.log(`💾 内容已保存 (层级: ${this.level}, 目标: ${this.target})`)
      }
    } catch (error) {
      console.error('保存失败:', error)
    }
  }

  /**
   * 静默保存内容
   */
  private async saveContentSilently(markdown: string) {
    try {
      if (this.config.savePageContent) {
        await this.config.savePageContent(this.target, markdown)
        console.log(`🔄 自动保存完成 (层级: ${this.level}, 目标: ${this.target})`)
      }
    } catch (error) {
      console.error('自动保存失败:', error)
    }
  }
}



/**
 * 预览插件的 Key
 */
export const previewPluginKey = new PluginKey<PreviewState>('wikilink-preview')

/**
 * 创建 WikiLink 预览插件
 */
export function createPreviewTooltip(config: WikiLinkConfig): Plugin {
  return new Plugin({
    key: previewPluginKey,
    
    state: {
      init() {
        return {
          target: null,
          visible: false,
          x: 0,
          y: 0,
        }
      },
      
      apply(tr, oldState) {
        const meta = tr.getMeta(previewPluginKey)
        if (meta) {
          return { ...oldState, ...meta }
        }
        return oldState
      },
    },
    
    view(editorView) {
      return new PreviewTooltipView(editorView, config)
    },
    
    props: {
      handleDOMEvents: {
        mouseover(view, event) {
          const target = event.target as HTMLElement
          // console.log(`🔥 [主编辑器V3] mouseover 事件 - 新版本已生效`)

          // 恢复简单的 WikiLink 处理
          if (target.hasAttribute('data-wikilink')) {
            const linkTarget = target.getAttribute('data-target')
            console.log(`🎯 [主编辑器V3] 悬浮 WikiLink: ${linkTarget}`)

            if (linkTarget) {
              const rect = target.getBoundingClientRect()

              // 智能预览管理：如果预览窗口还在且目标相同，取消隐藏；否则创建新预览
              const manager = NestedPreviewManager.getInstance()
              if (manager.hasActivePreview(linkTarget)) {
                console.log(`🔄 [主编辑器V3] 预览窗口还在，取消隐藏: ${linkTarget}`)
                manager.cancelHideAll()
                // 更新位置
                manager.updatePreviewPosition(linkTarget, rect.left + rect.width / 2, rect.bottom + 5)
              } else {
                console.log(`🆕 [主编辑器V3] 创建新预览: ${linkTarget}`)
                // 简单的预览显示
                view.dispatch(
                  view.state.tr.setMeta(previewPluginKey, {
                    target: linkTarget,
                    visible: true,
                    x: rect.left + rect.width / 2,
                    y: rect.bottom + 5,
                  })
                )
              }
            }
          }

          return false
        },

        mouseout(view, event) {
          // 主编辑器的预览隐藏交给全局管理器统一处理
          // 这样可以避免与全局管理器的隐藏逻辑冲突
          // console.log(`🎯 [主编辑器V3] mouseout 事件 - 交给全局管理器处理`)
          return false
        },

        // 添加ESC键处理
        keydown(view, event) {
          if (event.key === 'Escape') {
            const state = previewPluginKey.getState(view.state)
            if (state?.visible) {
              view.dispatch(
                view.state.tr.setMeta(previewPluginKey, {
                  target: null,
                  visible: false,
                  x: 0,
                  y: 0,
                })
              )
              return true // 阻止事件传播
            }
          }
          return false
        },

        // 添加点击其他地方隐藏预览
        click(view, event) {
          const target = event.target as HTMLElement
          const state = previewPluginKey.getState(view.state)

          // 如果点击的不是WikiLink且预览窗口可见，则隐藏预览
          if (state?.visible && !target.hasAttribute('data-wikilink') && !target.closest('.wikilink-preview-tooltip')) {
            view.dispatch(
              view.state.tr.setMeta(previewPluginKey, {
                target: null,
                visible: false,
                x: 0,
                y: 0,
              })
            )
          }

          return false
        },
      },
    },
  })
}

/**
 * 预览提示视图类 - 重构为使用嵌套管理器
 */
class PreviewTooltipView {
  private editorView: EditorView
  private config: WikiLinkConfig
  private manager: NestedPreviewManager
  private currentPreview: PreviewInstance | null = null

  constructor(editorView: EditorView, config: WikiLinkConfig) {
    this.editorView = editorView
    this.config = config
    this.manager = NestedPreviewManager.getInstance()

    // 注册到全局管理器，以便统一管理隐藏
    this.manager.registerMainPreviewManager(this)
  }
  
  update(view: EditorView, prevState: any) {
    const state = previewPluginKey.getState(view.state)
    const prevPreviewState = previewPluginKey.getState(prevState)

    // console.log(`🔄 [PreviewTooltipView] update 调用`)
    // console.log(`🔄 [PreviewTooltipView] 当前状态:`, state)
    // console.log(`🔄 [PreviewTooltipView] 之前状态:`, prevPreviewState)

    if (!state) {
      console.log(`⚠️ [PreviewTooltipView] 状态为空，返回`)
      return
    }

    // 如果预览状态发生变化
    if (state.visible !== prevPreviewState?.visible ||
        state.target !== prevPreviewState?.target) {

      // console.log(`🔄 [PreviewTooltipView] 状态发生变化`)

      if (state.visible && state.target) {
        // console.log(`🔄 [PreviewTooltipView] 显示预览: ${state.target}`)
        this.showPreview(state.target, state.x, state.y)
      } else {
        // console.log(`🔄 [PreviewTooltipView] 隐藏预览`)
        this.hidePreview()
      }
    } else {
      // console.log(`🔄 [PreviewTooltipView] 状态无变化`)
    }
  }

  destroy() {
    this.hidePreview()
    this.manager.clearAll()
  }

  /**
   * 显示预览
   */
  private showPreview(target: string, x: number, y: number) {
    // console.log(`📺 [showPreview] 准备显示预览: ${target}`)
    // console.log(`📺 [showPreview] 当前预览:`, this.currentPreview?.target)

    // 如果已经显示相同目标，更新位置并刷新内容
    if (this.currentPreview && this.currentPreview.target === target) {
      // console.log(`📺 [showPreview] 复用现有预览，更新位置和内容`)
      this.currentPreview.updatePosition(x, y)
      this.currentPreview.refreshContent()  // 🔄 刷新内容确保显示最新
      return
    }

    // 隐藏当前预览
    // console.log(`📺 [showPreview] 隐藏当前预览`)
    this.hidePreview()

    // 创建新预览（根级预览，level = 0）
    // console.log(`📺 [showPreview] 创建新预览: ${target}`)
    this.currentPreview = this.manager.createPreview(target, x, y, this.config, 0)
    // console.log(`📺 [showPreview] 新预览创建结果:`, this.currentPreview)
  }

  /**
   * 隐藏预览 - 公共方法，供全局管理器调用
   */
  public hidePreview() {
    if (this.currentPreview) {
      this.manager.removePreview(this.currentPreview)
      this.currentPreview = null
    }

    // 安全地更新 ProseMirror 状态，避免编辑器已销毁的错误
    try {
      if (this.editorView && !this.editorView.isDestroyed) {
        this.editorView.dispatch(
          this.editorView.state.tr.setMeta(previewPluginKey, {
            target: null,
            visible: false,
            x: 0,
            y: 0,
          })
        )
        // console.log(`🎯 [PreviewTooltipView] 已重置编辑器预览状态`)
      }
    } catch (error) {
      // console.warn(`⚠️ [PreviewTooltipView] 重置编辑器状态时出错:`, error)
    }
  }

  /**
   * 获取当前预览目标 - 供全局管理器调用
   */
  public getCurrentPreviewTarget(): string | null {
    return this.currentPreview?.target || null
  }

  /**
   * 更新当前预览位置 - 供全局管理器调用
   */
  public updateCurrentPreviewPosition(x: number, y: number) {
    if (this.currentPreview) {
      this.currentPreview.updatePosition(x, y)
    }
  }
}

export default createPreviewTooltip

