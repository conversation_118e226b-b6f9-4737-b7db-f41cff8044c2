<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PaoLife Inbox</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://unpkg.com/lucide@latest"></script>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        ::-webkit-scrollbar { width: 8px; }
        ::-webkit-scrollbar-track { background: #1e293b; }
        ::-webkit-scrollbar-thumb { background: #475569; border-radius: 4px; }
        ::-webkit-scrollbar-thumb:hover { background: #64748b; }
        
        /* Dropdown animation */
        .dropdown-menu {
            display: none;
            transform-origin: top right;
            animation: scale-in 0.1s ease-out forwards;
        }
        .dropdown-menu.open {
            display: block;
        }
        @keyframes scale-in {
            from { opacity: 0; transform: scale(0.95); }
            to { opacity: 1; transform: scale(1); }
        }
    </style>
</head>
<body class="bg-slate-900 text-slate-300">

    <div class="flex h-screen">
        <!-- Sidebar Navigation -->
        <aside class="w-64 flex-shrink-0 bg-slate-950/70 border-r border-slate-800 flex flex-col">
            <div class="h-16 flex items-center px-6">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-indigo-400"><path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"/><path d="m9 12 2 2 4-4"/></svg>
                <h1 class="text-xl font-bold ml-2 text-white">PaoLife</h1>
            </div>
            <nav class="flex-1 px-4 py-4 space-y-2">
                <a href="#" class="flex items-center px-4 py-2 rounded-lg hover:bg-slate-800 transition-colors duration-200">
                    <i data-lucide="layout-dashboard" class="w-5 h-5 mr-3"></i>
                    仪表盘
                </a>
                <a href="#" class="flex items-center px-4 py-2 text-white bg-indigo-500/20 border border-indigo-500/30 rounded-lg">
                    <i data-lucide="inbox" class="w-5 h-5 mr-3"></i>
                    收件箱
                </a>
                <a href="#" class="flex items-center px-4 py-2 rounded-lg hover:bg-slate-800 transition-colors duration-200">
                    <i data-lucide="kanban-square" class="w-5 h-5 mr-3"></i>
                    项目管理
                </a>
                <a href="#" class="flex items-center px-4 py-2 rounded-lg hover:bg-slate-800 transition-colors duration-200">
                    <i data-lucide="target" class="w-5 h-5 mr-3"></i>
                    领域管理
                </a>
                <a href="#" class="flex items-center px-4 py-2 rounded-lg hover:bg-slate-800 transition-colors duration-200">
                    <i data-lucide="library" class="w-5 h-5 mr-3"></i>
                    资源库
                </a>
                <a href="#" class="flex items-center px-4 py-2 rounded-lg hover:bg-slate-800 transition-colors duration-200">
                    <i data-lucide="archive" class="w-5 h-5 mr-3"></i>
                    归档管理
                </a>
                 <a href="#" class="flex items-center px-4 py-2 rounded-lg hover:bg-slate-800 transition-colors duration-200">
                    <i data-lucide="book-check" class="w-5 h-5 mr-3"></i>
                    复盘总结
                </a>
            </nav>
            <div class="px-4 py-4 border-t border-slate-800">
                 <a href="#" class="flex items-center px-4 py-2 rounded-lg hover:bg-slate-800 transition-colors duration-200">
                    <i data-lucide="settings" class="w-5 h-5 mr-3"></i>
                    设置
                </a>
                <div class="flex items-center mt-4 px-4">
                    <img class="h-10 w-10 rounded-full" src="https://placehold.co/100x100/6366f1/e0e7ff?text=User" alt="User Avatar">
                    <div class="ml-3">
                        <p class="text-sm font-medium text-white">张伟</p>
                        <p class="text-xs text-slate-400">Pro Plan</p>
                    </div>
                </div>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="flex-1 overflow-y-auto p-6 lg:p-8">
            <div class="max-w-4xl mx-auto">
                <!-- Header -->
                <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
                    <div>
                        <h1 class="text-3xl font-bold text-white flex items-center">
                            <i data-lucide="inbox" class="w-8 h-8 mr-3 text-indigo-400"></i>
                            收件箱
                        </h1>
                        <p class="text-slate-400 mt-1">捕获所有未经组织的灵感、想法和笔记。</p>
                    </div>
                    <button class="mt-4 md:mt-0 bg-indigo-600 text-white font-semibold px-4 py-2 rounded-lg hover:bg-indigo-500 transition-colors duration-200 whitespace-nowrap flex items-center">
                        <i data-lucide="calendar-plus" class="w-5 h-5 mr-2"></i>
                        创建今日笔记
                    </button>
                </div>

                <!-- Inbox Notes List -->
                <div class="space-y-4" id="inbox-list">
                    <!-- Inbox notes will be dynamically inserted here -->
                </div>
            </div>
        </main>
    </div>

    <script>
        // --- MOCK DATA ---
        // Based on your InboxNote Prisma schema
        const mockInboxNotes = [
            { id: 1, title: '2025-08-15.md', content: '今天完成了仪表盘和收件箱的原型设计，感觉很棒。下午和产品经理讨论了下一个季度的 OKR，需要花时间消化一下...', isDaily: true, processed: false, createdAt: new Date('2025-08-15T18:30:00') },
            { id: 2, title: null, content: 'PaoLife 的 Slogan 可以考虑用 "你的第二大脑，重塑人生秩序"。', isDaily: false, processed: false, createdAt: new Date('2025-08-15T11:45:00') },
            { id: 3, title: '关于 Milkdown 编辑器的调研', content: '优点是基于 Prosemirror，插件化架构，高度可定制。缺点是社区相对较小，遇到问题可能需要自己深入研究。', isDaily: false, processed: false, createdAt: new Date('2025-08-14T16:20:00') },
            { id: 4, title: null, content: '记得提醒自己下周一之前要续费服务器。', isDaily: false, processed: false, createdAt: new Date('2025-08-14T09:10:00') },
            { id: 5, title: '一个关于习惯养成功能的想法', content: '可以加入一个“习惯中断”的选项，比如旅行或者生病，这样就不会影响整体的完成率统计。', isDaily: false, processed: true, createdAt: new Date('2025-08-13T22:05:00') },
        ];

        // --- UTILITY FUNCTIONS ---
        const timeAgo = (date) => {
            const seconds = Math.floor((new Date() - date) / 1000);
            let interval = seconds / 31536000;
            if (interval > 1) return Math.floor(interval) + " 年前";
            interval = seconds / 2592000;
            if (interval > 1) return Math.floor(interval) + " 月前";
            interval = seconds / 86400;
            if (interval > 1) return Math.floor(interval) + " 天前";
            interval = seconds / 3600;
            if (interval > 1) return Math.floor(interval) + " 小时前";
            interval = seconds / 60;
            if (interval > 1) return Math.floor(interval) + " 分钟前";
            return Math.floor(seconds) + " 秒前";
        };

        // --- RENDER FUNCTIONS ---
        const renderInboxNotes = () => {
            const container = document.getElementById('inbox-list');
            container.innerHTML = '';

            mockInboxNotes.forEach(note => {
                const isProcessed = note.processed;
                const noteHTML = `
                    <div class="bg-slate-800/50 border ${isProcessed ? 'border-slate-800' : 'border-slate-700/50'} rounded-lg shadow-lg transition-all duration-300 ${isProcessed ? 'opacity-50' : ''}">
                        <div class="p-5">
                            ${note.isDaily ? `<div class="flex items-center text-sm text-yellow-400 mb-2"><i data-lucide="star" class="w-4 h-4 mr-2 fill-current"></i><span>每日笔记</span></div>` : ''}
                            <p class="text-white font-medium">${note.title || '无标题笔记'}</p>
                            <p class="text-slate-400 mt-1 text-sm">${note.content}</p>
                        </div>
                        <div class="px-5 py-3 bg-slate-800/60 border-t border-slate-700/50 flex justify-between items-center">
                            <span class="text-xs text-slate-500">创建于 ${timeAgo(note.createdAt)}</span>
                            <div class="flex items-center space-x-2">
                                <!-- Action Buttons -->
                                <button title="转化为任务" class="p-2 rounded-md hover:bg-slate-700 text-slate-400 hover:text-white transition-colors">
                                    <i data-lucide="check-square" class="w-4 h-4"></i>
                                </button>
                                <div class="relative">
                                    <button title="移动到..." class="p-2 rounded-md hover:bg-slate-700 text-slate-400 hover:text-white transition-colors dropdown-toggle">
                                        <i data-lucide="move-right" class="w-4 h-4"></i>
                                    </button>
                                    <!-- Dropdown Menu -->
                                    <div class="dropdown-menu absolute bottom-full right-0 mb-2 w-48 bg-slate-700 border border-slate-600 rounded-md shadow-xl z-10">
                                        <a href="#" class="flex items-center px-4 py-2 text-sm text-slate-300 hover:bg-slate-600"><i data-lucide="library" class="w-4 h-4 mr-2"></i> 移动到资源库</a>
                                        <a href="#" class="flex items-center px-4 py-2 text-sm text-slate-300 hover:bg-slate-600"><i data-lucide="kanban-square" class="w-4 h-4 mr-2"></i> 关联到项目</a>
                                        <a href="#" class="flex items-center px-4 py-2 text-sm text-slate-300 hover:bg-slate-600"><i data-lucide="target" class="w-4 h-4 mr-2"></i> 关联到领域</a>
                                    </div>
                                </div>
                                <button title="归档" class="p-2 rounded-md hover:bg-slate-700 text-slate-400 hover:text-white transition-colors">
                                    <i data-lucide="archive" class="w-4 h-4"></i>
                                </button>
                                <button title="删除" class="p-2 rounded-md hover:bg-slate-700 text-rose-400 hover:text-rose-300 transition-colors">
                                    <i data-lucide="trash-2" class="w-4 h-4"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                `;
                container.innerHTML += noteHTML;
            });
        };

        // --- INITIALIZATION & EVENT LISTENERS ---
        document.addEventListener('DOMContentLoaded', () => {
            renderInboxNotes();
            lucide.createIcons();

            // Dropdown toggle logic
            document.querySelectorAll('.dropdown-toggle').forEach(toggle => {
                toggle.addEventListener('click', (event) => {
                    event.stopPropagation();
                    const menu = toggle.nextElementSibling;
                    // Close all other menus
                    document.querySelectorAll('.dropdown-menu.open').forEach(openMenu => {
                        if (openMenu !== menu) {
                            openMenu.classList.remove('open');
                        }
                    });
                    menu.classList.toggle('open');
                });
            });

            // Close dropdowns when clicking outside
            window.addEventListener('click', () => {
                document.querySelectorAll('.dropdown-menu.open').forEach(menu => {
                    menu.classList.remove('open');
                });
            });
        });

    </script>
</body>
</html>
