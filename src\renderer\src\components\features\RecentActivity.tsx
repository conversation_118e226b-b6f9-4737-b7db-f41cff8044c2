import { useMemo } from 'react'
import { <PERSON> } from 'react-router-dom'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { Button } from '../ui/button'
import { cn } from '../../lib/utils'
import { useProjectStore } from '../../store/projectStore'
import { useAreaStore } from '../../store/areaStore'
import { useTaskStore } from '../../store/taskStore'

interface ActivityItem {
  id: string
  type: 'project' | 'area' | 'task' | 'resource'
  title: string
  description?: string
  updatedAt: string
  status?: string
  link: string
  icon: string
  color: string
}

interface RecentActivityProps {
  className?: string
  maxItems?: number
}

export function RecentActivity({ className, maxItems = 8 }: RecentActivityProps) {
  const { projects } = useProjectStore()
  const { areas } = useAreaStore()
  const { tasks } = useTaskStore()

  // Combine and sort all recent activities
  const recentActivities = useMemo(() => {
    const activities: ActivityItem[] = []

    // Add projects
    projects.forEach((project) => {
      if (!project.archived) {
        activities.push({
          id: project.id,
          type: 'project',
          title: project.name,
          description: project.description || undefined,
          updatedAt:
            project.updatedAt instanceof Date
              ? project.updatedAt.toISOString()
              : new Date(project.updatedAt).toISOString(),
          status: project.status,
          link: `/projects/${project.id}`,
          icon: '📋',
          color: 'bg-project/10 text-project border-project/20'
        })
      }
    })

    // Add areas
    areas.forEach((area) => {
      if (!area.archived) {
        activities.push({
          id: area.id,
          type: 'area',
          title: area.name,
          description: area.description || undefined,
          updatedAt:
            area.updatedAt instanceof Date
              ? area.updatedAt.toISOString()
              : new Date(area.updatedAt).toISOString(),
          link: `/areas/${area.id}`,
          icon: '🏠',
          color: 'bg-area/10 text-area border-area/20'
        })
      }
    })

    // Add recent tasks
    tasks.slice(0, 5).forEach((task) => {
      activities.push({
        id: task.id,
        type: 'task',
        title: task.content,
        description: task.description || undefined,
        updatedAt:
          task.updatedAt instanceof Date
            ? task.updatedAt.toISOString()
            : new Date(task.updatedAt).toISOString(),
        status: task.completed ? '已完成' : '待处理',
        link: task.projectId ? `/projects/${task.projectId}` : '/inbox',
        icon: task.completed ? '✅' : '📝',
        color: task.completed
          ? 'bg-green-50 text-green-700 border-green-200'
          : 'bg-blue-50 text-blue-700 border-blue-200'
      })
    })

    // Sort by updatedAt and limit
    return activities
      .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
      .slice(0, maxItems)
  }, [projects, areas, tasks, maxItems])

  const getRelativeTime = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

    if (diffInSeconds < 60) return '刚刚'
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}分钟前`
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}小时前`
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}天前`
    return date.toLocaleDateString()
  }

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'project':
        return '项目'
      case 'area':
        return '领域'
      case 'task':
        return '任务'
      case 'resource':
        return '资源'
      default:
        return type
    }
  }

  return (
    <div className={cn('w-full', className)}>
      <div className="pb-3">
        <div className="flex items-center justify-between">
          <Badge variant="outline" className="text-xs">
            {recentActivities.length} 项
          </Badge>
        </div>
      </div>

      <div>
        {recentActivities.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <div className="text-4xl mb-2">🌱</div>
            <p className="text-sm">暂无最近活动</p>
            <p className="text-xs mt-1">开始处理您的项目和领域吧！</p>
          </div>
        ) : (
          <div className="space-y-3">
            {recentActivities.map((activity) => (
              <div
                key={`${activity.type}-${activity.id}`}
                className="flex items-start gap-3 p-3 rounded-lg border bg-card hover:bg-accent/50 transition-colors"
              >
                <div className="flex-shrink-0 mt-0.5">
                  <div
                    className={cn(
                      'w-8 h-8 rounded-lg flex items-center justify-center text-sm',
                      activity.color
                    )}
                  >
                    {activity.icon}
                  </div>
                </div>

                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between gap-2">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h3 className="font-medium text-sm truncate">{activity.title}</h3>
                        <Badge variant="outline" className="text-xs">
                          {getTypeLabel(activity.type)}
                        </Badge>
                        {activity.status && (
                          <Badge variant="secondary" className="text-xs">
                            {activity.status}
                          </Badge>
                        )}
                      </div>
                      {activity.description && (
                        <p className="text-xs text-muted-foreground line-clamp-2">
                          {activity.description}
                        </p>
                      )}
                    </div>

                    <div className="text-xs text-muted-foreground flex-shrink-0">
                      {getRelativeTime(activity.updatedAt)}
                    </div>
                  </div>

                  <div className="mt-2">
                    <Button asChild variant="ghost" size="sm" className="h-6 px-2 text-xs">
                      <Link to={activity.link}>打开</Link>
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {recentActivities.length > 0 && (
          <div className="mt-4 pt-3 border-t">
            <div className="grid grid-cols-2 gap-2">
              <Button asChild variant="outline" size="sm">
                <Link to="/projects">所有项目</Link>
              </Button>
              <Button asChild variant="outline" size="sm">
                <Link to="/areas">所有领域</Link>
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default RecentActivity
