import { DatabaseService } from './database'
import { reviewTemplatesI18n } from '../shared/i18n/reviews'


// i18n support for seeding system review templates
// Locale detection: PAOLIFE_LANG env > OS locale > default 'en'
function detectLocale(): 'zh' | 'en' {
  const env = (
    process.env.PAOLIFE_LANG ||
    process.env.LANG ||
    process.env.LC_ALL ||
    ''
  ).toLowerCase()
  if (env.includes('zh')) return 'zh'
  return 'en'
}




const defaultTemplates = (() => {
  const locale = detectLocale()
  const T = reviewTemplatesI18n[locale]
  return [
    {
      name: T.daily.name,
      description: T.daily.description,
      type: 'daily',
      isDefault: true,
      isSystem: true,
      structure: {
        sections: [
          { id: 'wins', title: T.daily.sections.wins.title, description: T.daily.sections.wins.description, placeholder: T.daily.sections.wins.placeholder, required: true },
          { id: 'challenges', title: T.daily.sections.challenges.title, description: T.daily.sections.challenges.description, placeholder: T.daily.sections.challenges.placeholder, required: false },
          { id: 'learnings', title: T.daily.sections.learnings.title, description: T.daily.sections.learnings.description, placeholder: T.daily.sections.learnings.placeholder, required: false },
          { id: 'tomorrow', title: T.daily.sections.tomorrow.title, description: T.daily.sections.tomorrow.description, placeholder: T.daily.sections.tomorrow.placeholder, required: true },
        ],
      },
    },
    {
      name: T.weekly.name,
      description: T.weekly.description,
      type: 'weekly',
      isDefault: true,
      isSystem: true,
      structure: {
        sections: [
          { id: 'achievements', title: T.weekly.sections.achievements.title, description: T.weekly.sections.achievements.description, placeholder: T.weekly.sections.achievements.placeholder, required: true },
          { id: 'challenges', title: T.weekly.sections.challenges.title, description: T.weekly.sections.challenges.description, placeholder: T.weekly.sections.challenges.placeholder, required: false },
          { id: 'learnings', title: T.weekly.sections.learnings.title, description: T.weekly.sections.learnings.description, placeholder: T.weekly.sections.learnings.placeholder, required: false },
          { id: 'nextWeek', title: T.weekly.sections.nextWeek.title, description: T.weekly.sections.nextWeek.description, placeholder: T.weekly.sections.nextWeek.placeholder, required: true },
          { id: 'improvements', title: T.weekly.sections.improvements.title, description: T.weekly.sections.improvements.description, placeholder: T.weekly.sections.improvements.placeholder, required: false },
        ],
      },
    },
    {
      name: T.monthly.name,
      description: T.monthly.description,
      type: 'monthly',
      isDefault: true,
      isSystem: true,
      structure: {
        sections: [
          { id: 'progress', title: T.monthly.sections.progress.title, description: T.monthly.sections.progress.description, placeholder: T.monthly.sections.progress.placeholder, required: true },
          { id: 'highlights', title: T.monthly.sections.highlights.title, description: T.monthly.sections.highlights.description, placeholder: T.monthly.sections.highlights.placeholder, required: true },
          { id: 'challenges', title: T.monthly.sections.challenges.title, description: T.monthly.sections.challenges.description, placeholder: T.monthly.sections.challenges.placeholder, required: false },
          { id: 'nextMonth', title: T.monthly.sections.nextMonth.title, description: T.monthly.sections.nextMonth.description, placeholder: T.monthly.sections.nextMonth.placeholder, required: true },
          { id: 'metrics', title: T.monthly.sections.metrics.title, description: T.monthly.sections.metrics.description, placeholder: T.monthly.sections.metrics.placeholder, required: false },
        ],
      },
    },
    {
      name: T.quarterly.name,
      description: T.quarterly.description,
      type: 'quarterly',
      isDefault: true,
      isSystem: true,
      structure: {
        sections: [
          { id: 'objectives', title: T.quarterly.sections.objectives.title, description: T.quarterly.sections.objectives.description, placeholder: T.quarterly.sections.objectives.placeholder, required: true },
          { id: 'wins', title: T.quarterly.sections.wins.title, description: T.quarterly.sections.wins.description, placeholder: T.quarterly.sections.wins.placeholder, required: true },
          { id: 'lessons', title: T.quarterly.sections.lessons.title, description: T.quarterly.sections.lessons.description, placeholder: T.quarterly.sections.lessons.placeholder, required: true },
          { id: 'nextQuarter', title: T.quarterly.sections.nextQuarter.title, description: T.quarterly.sections.nextQuarter.description, placeholder: T.quarterly.sections.nextQuarter.placeholder, required: true },
        ],
      },
    },
    {
      name: T.yearly.name,
      description: T.yearly.description,
      type: 'yearly',
      isDefault: true,
      isSystem: true,
      structure: {
        sections: [
          { id: 'yearOverview', title: T.yearly.sections.yearOverview.title, description: T.yearly.sections.yearOverview.description, placeholder: T.yearly.sections.yearOverview.placeholder, required: true },
          { id: 'achievements', title: T.yearly.sections.achievements.title, description: T.yearly.sections.achievements.description, placeholder: T.yearly.sections.achievements.placeholder, required: true },
          { id: 'growth', title: T.yearly.sections.growth.title, description: T.yearly.sections.growth.description, placeholder: T.yearly.sections.growth.placeholder, required: true },
          { id: 'challenges', title: T.yearly.sections.challenges.title, description: T.yearly.sections.challenges.description, placeholder: T.yearly.sections.challenges.placeholder, required: false },
          { id: 'nextYear', title: T.yearly.sections.nextYear.title, description: T.yearly.sections.nextYear.description, placeholder: T.yearly.sections.nextYear.placeholder, required: true },
        ],
      },
    },
  ]
})()

export async function seedReviewTemplates(database: DatabaseService): Promise<void> {
  console.log('Seeding review templates...')

  try {
    // Check if templates already exist
    const existingTemplates = await database.getReviewTemplates()
    if (existingTemplates.success && existingTemplates.data && existingTemplates.data.length > 0) {
      console.log('Review templates already exist, skipping seed')
      return
    }

    // Create default templates
    for (const template of defaultTemplates) {
      const result = await database.createReviewTemplate(template)
      if (result.success) {
        console.log(`Created template: ${template.name}`)
      } else {
        console.error(`Failed to create template ${template.name}:`, result.error)
      }
    }

    console.log('Review templates seeded successfully')
  } catch (error) {
    console.error('Error seeding review templates:', error)
  }
}
