import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from '../components/ui/card'
import { Badge } from '../components/ui/badge'
import { Progress } from '../components/ui/progress'
import { Button } from '../components/ui/button'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '../components/ui/tabs'
import { Calendar, Clock, Target, TrendingUp, Users, BookOpen, Archive, Inbox } from 'lucide-react'
import TodayTasks from '../components/features/TodayTasks'
import UpcomingProjects from '../components/features/UpcomingProjects'
import RecentActivity from '../components/features/RecentActivity'
import { InspirationCard } from '../components/features/InspirationCard'
import { useProjectStore } from '../store/projectStore'
import { useAreaStore } from '../store/areaStore'
import { useLanguage } from '../contexts/LanguageContext'
import { useTaskStore } from '../store/taskStore'
import { useResourceStore } from '../store/resourceStore'
import { usePARASettingsStore } from '../store/paraSettingsStore'
import { useUserSettingsStore } from '../store/userSettingsStore'
import { useNavigate } from 'react-router-dom'
import { useMemo, useState, useEffect } from 'react'
import { useUIStore } from '../store/uiStore'
import CreateProjectDialog from '../components/features/CreateProjectDialog'
import CreateAreaDialog from '../components/features/CreateAreaDialog'
import { databaseApi } from '../lib/api'

// 收件箱项目类型
interface InboxItem {
  id: string
  content: string
  type: 'note' | 'task' | 'idea' | 'link' | 'file'
  priority: 'low' | 'medium' | 'high'
  tags: string[]
  processed: boolean
  processedAt?: string
  processedTo?: {
    type: 'project' | 'area' | 'resource' | 'archive'
    id: string
    name: string
  }
  createdAt: string
  updatedAt: string
}

export function DashboardPage() {
  const { projects } = useProjectStore()
  const { areas, habits, habitRecords, fetchAllHabitsAndRecords } = useAreaStore()
  const { tasks } = useTaskStore()
  const { resources } = useResourceStore()
  const { settings, updateWeeklyReview } = usePARASettingsStore()
  const { settings: userSettings } = useUserSettingsStore()
  const { addNotification } = useUIStore()
  const { t } = useLanguage()
  const navigate = useNavigate()

  // 快速捕捉状态
  const [quickInputContent, setQuickInputContent] = useState('')
  const [isSubmittingQuickInput, setIsSubmittingQuickInput] = useState(false)
  const [showTagSuggestions, setShowTagSuggestions] = useState(false)
  const [tagSuggestionIndex, setTagSuggestionIndex] = useState(0)
  const [currentTagInput, setCurrentTagInput] = useState('')

  // 对话框状态
  const [isCreateProjectDialogOpen, setIsCreateProjectDialogOpen] = useState(false)
  const [isCreateAreaDialogOpen, setIsCreateAreaDialogOpen] = useState(false)

  // 收件箱数据状态
  const [inboxItemsData, setInboxItemsData] = useState<InboxItem[]>(() => {
    try {
      const saved = localStorage.getItem('paolife-inbox-items')
      return saved ? JSON.parse(saved) : []
    } catch {
      return []
    }
  })

  // 监听localStorage变化和自定义事件
  useEffect(() => {
    const handleStorageChange = () => {
      try {
        const saved = localStorage.getItem('paolife-inbox-items')
        setInboxItemsData(saved ? JSON.parse(saved) : [])
      } catch {
        setInboxItemsData([])
      }
    }

    const handleInspirationAdded = () => {
      handleStorageChange()
    }

    window.addEventListener('storage', handleStorageChange)
    document.addEventListener('inspiration-added', handleInspirationAdded)

    return () => {
      window.removeEventListener('storage', handleStorageChange)
      document.removeEventListener('inspiration-added', handleInspirationAdded)
    }
  }, [])

  // 初始化数据加载
  useEffect(() => {
    const initializeData = async () => {
      try {
        console.log('Dashboard: Initializing habit data...')
        console.log(`Areas: ${areas.length}, Habits: ${habits.length}, Records: ${habitRecords.length}`)
        // 加载所有习惯和习惯记录数据
        await fetchAllHabitsAndRecords()
      } catch (error) {
        console.error('Failed to initialize dashboard data:', error)
      }
    }

    // 当有领域数据时，总是尝试加载习惯数据（确保数据是最新的）
    if (areas.length > 0) {
      console.log('Dashboard: Triggering habit data load')
      initializeData()
    }
  }, [areas.length, fetchAllHabitsAndRecords])

  // 监听习惯记录变化，实时同步数据
  useEffect(() => {
    const handleHabitRecordChange = () => {
      // 当习惯记录发生变化时，重新加载数据
      if (areas.length > 0) {
        fetchAllHabitsAndRecords()
      }
    }

    // 监听自定义事件
    document.addEventListener('habit-record-changed', handleHabitRecordChange)

    return () => {
      document.removeEventListener('habit-record-changed', handleHabitRecordChange)
    }
  }, [areas.length, fetchAllHabitsAndRecords])

  // 常用标签
  const baseTags = ['工作', '学习', '生活', '想法', '待办', '重要', '创意', '灵感']

  // 获取用户自定义标签
  const getUserTags = (): string[] => {
    try {
      const saved = localStorage.getItem('paolife-user-tags')
      return saved ? JSON.parse(saved) : []
    } catch {
      return []
    }
  }

  // 保存用户标签
  const saveUserTag = (tag: string) => {
    try {
      const userTags = getUserTags()
      if (!userTags.includes(tag)) {
        const updatedTags = [...userTags, tag]
        localStorage.setItem('paolife-user-tags', JSON.stringify(updatedTags))
      }
    } catch (error) {
      console.error('Failed to save user tag:', error)
    }
  }

  // 合并所有标签
  const commonTags = [...baseTags, ...getUserTags()]

  // 判断是否为数值型习惯
  const isNumericHabit = (habit: any): boolean => {
    return habit.target > 1
  }

  // 快速捕捉提交处理
  const handleQuickInputSubmit = async () => {
    if (!quickInputContent.trim() || isSubmittingQuickInput) return

    setIsSubmittingQuickInput(true)
    try {
      // 解析标签
      const tags: string[] = []
      const content = quickInputContent.replace(/#(\w+)/g, (match, tag) => {
        tags.push(tag)
        return ''
      }).trim()

      if (!content) {
        addNotification({
          type: 'warning',
          title: '内容为空',
          message: '请输入一些内容'
        })
        return
      }

      // 创建收件箱项目
      const newItem = {
        id: Date.now().toString(),
        content,
        tags,
        type: 'note' as const,
        processed: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }

      // 保存到localStorage
      const existingItems = JSON.parse(localStorage.getItem('paolife-inbox-items') || '[]')
      const updatedItems = [newItem, ...existingItems]
      localStorage.setItem('paolife-inbox-items', JSON.stringify(updatedItems))

      // 触发自定义事件通知收件箱页面刷新
      document.dispatchEvent(new CustomEvent('inspiration-added', { detail: newItem }))

      setQuickInputContent('')
      setShowTagSuggestions(false)
      setCurrentTagInput('')

      addNotification({
        type: 'success',
        title: '灵感已记录',
        message: tags.length > 0 ? `已添加到收件箱，包含${tags.length}个标签` : '已添加到收件箱'
      })
    } catch (error) {
      console.error('Failed to create quick input item:', error)
      addNotification({
        type: 'error',
        title: '记录失败',
        message: '请重试'
      })
    } finally {
      setIsSubmittingQuickInput(false)
    }
  }

  // 键盘事件处理
  const handleQuickInputKeyDown = (e: React.KeyboardEvent) => {
    if (showTagSuggestions) {
      const filteredTags = commonTags.filter(tag => tag.toLowerCase().includes(currentTagInput.toLowerCase()))
      const hasNewTag = currentTagInput && !commonTags.some(tag => tag.toLowerCase().includes(currentTagInput.toLowerCase()))
      const totalOptions = filteredTags.length + (hasNewTag ? 1 : 0)

      if (e.key === 'ArrowDown') {
        e.preventDefault()
        setTagSuggestionIndex((prev) => (prev + 1) % totalOptions)
      } else if (e.key === 'ArrowUp') {
        e.preventDefault()
        setTagSuggestionIndex((prev) => (prev - 1 + totalOptions) % totalOptions)
      } else if (e.key === 'Enter') {
        e.preventDefault()
        if (hasNewTag && tagSuggestionIndex === filteredTags.length) {
          handleTagSelect(currentTagInput)
        } else if (filteredTags.length > 0 && tagSuggestionIndex < filteredTags.length) {
          handleTagSelect(filteredTags[tagSuggestionIndex])
        }
      } else if (e.key === 'Escape') {
        setShowTagSuggestions(false)
        setCurrentTagInput('')
      }
    } else if (e.key === 'Enter' && e.shiftKey) {
      e.preventDefault()
      handleQuickInputSubmit()
    }
  }

  // 输入变化处理
  const handleQuickInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setQuickInputContent(value)

    // 检查是否输入了#来触发标签选择
    const lastChar = value[value.length - 1]
    const beforeLastChar = value[value.length - 2]

    if (lastChar === '#' && (beforeLastChar === ' ' || beforeLastChar === undefined || value.length === 1)) {
      setShowTagSuggestions(true)
      setTagSuggestionIndex(0)
      setCurrentTagInput('')
    } else if (showTagSuggestions) {
      const hashIndex = value.lastIndexOf('#')
      if (hashIndex !== -1) {
        const tagInput = value.slice(hashIndex + 1)
        setCurrentTagInput(tagInput)

        if (tagInput.includes(' ')) {
          const tag = tagInput.split(' ')[0]
          if (tag) {
            handleTagSelect(tag)
          }
        }
      } else {
        setShowTagSuggestions(false)
        setCurrentTagInput('')
      }
    }
  }

  // 标签选择处理
  const handleTagSelect = (tag: string) => {
    if (!baseTags.includes(tag) && !getUserTags().includes(tag)) {
      saveUserTag(tag)
    }

    const hashIndex = quickInputContent.lastIndexOf('#')
    if (hashIndex !== -1) {
      const beforeHash = quickInputContent.slice(0, hashIndex)
      const afterTag = quickInputContent.slice(hashIndex + 1 + currentTagInput.length)
      setQuickInputContent(`${beforeHash}#${tag} ${afterTag}`)
    }
    setShowTagSuggestions(false)
    setCurrentTagInput('')
  }

  // 创建项目处理
  const handleCreateProject = async (projectData: any) => {
    try {
      const result = await databaseApi.createProject(projectData)
      if (result.success) {
        addNotification({
          type: 'success',
          title: '项目创建成功',
          message: `项目 "${projectData.name}" 已创建`
        })
        setIsCreateProjectDialogOpen(false)
      } else {
        throw new Error(result.error || '创建项目失败')
      }
    } catch (error) {
      console.error('Failed to create project:', error)
      addNotification({
        type: 'error',
        title: '创建项目失败',
        message: '请重试'
      })
    }
  }

  // 创建领域处理
  const handleCreateArea = async (areaData: any) => {
    try {
      const result = await databaseApi.createArea(areaData)
      if (result.success) {
        addNotification({
          type: 'success',
          title: '领域创建成功',
          message: `领域 "${areaData.name}" 已创建`
        })
        setIsCreateAreaDialogOpen(false)
      } else {
        throw new Error(result.error || '创建领域失败')
      }
    } catch (error) {
      console.error('Failed to create area:', error)
      addNotification({
        type: 'error',
        title: '创建领域失败',
        message: '请重试'
      })
    }
  }

  // Handle review completion
  const handleStartReview = () => {
    // Update last review date when starting a review
    updateWeeklyReview({ lastReviewDate: new Date().toISOString() })
    navigate('/reviews')
  }

  // Calculate stats
  const activeProjects = projects.filter((p) => !p.archived && p.status !== 'Completed').length
  const completedProjects = projects.filter((p) => p.status === 'Completed').length
  const totalAreas = areas.filter((a) => !a.archived).length
  const inboxItems = inboxItemsData.filter((item: InboxItem) => !item.processed).length
  const totalResources = resources.length

  // Calculate weekly review status
  const isWeeklyReviewDue = () => {
    if (!settings.weeklyReview.enabled) return false

    const today = new Date()
    const currentDay = today.getDay()
    const reviewDay = settings.weeklyReview.dayOfWeek === 'sunday' ? 0 :
                     settings.weeklyReview.dayOfWeek === 'monday' ? 1 : 5

    // Check if today is the review day
    if (currentDay !== reviewDay) return false

    // Check if we already did a review this week
    const lastReviewDate = settings.weeklyReview.lastReviewDate
    if (lastReviewDate) {
      const lastReview = new Date(lastReviewDate)
      const weekStart = new Date(today)
      weekStart.setDate(today.getDate() - currentDay) // Start of current week
      weekStart.setHours(0, 0, 0, 0)

      // If last review was this week, don't show reminder
      if (lastReview >= weekStart) return false
    }

    return true
  }

  // Get current time greeting
  const getTimeGreeting = () => {
    const hour = new Date().getHours()
    if (hour < 12) return '早上好'
    if (hour < 18) return '下午好'
    return '晚上好'
  }

  // Get current date string
  const getCurrentDateString = () => {
    const now = new Date()
    const year = now.getFullYear()
    const month = now.getMonth() + 1
    const day = now.getDate()
    return `${year}年${month}月${day}日`
  }

  // Calculate habit completion rate for an area
  const getAreaHabitCompletionRate = (areaId: string) => {
    const areaHabits = habits.filter(h => h.areaId === areaId)
    if (areaHabits.length === 0) return 0

    const today = new Date()
    const weekStart = new Date(today)
    weekStart.setDate(today.getDate() - today.getDay()) // Start of current week
    weekStart.setHours(0, 0, 0, 0)

    let totalPossibleCompletions = 0
    let actualCompletions = 0

    areaHabits.forEach(habit => {
      // Calculate how many times this habit should have been completed this week
      const daysInWeek = Math.min(7, Math.ceil((today.getTime() - weekStart.getTime()) / (1000 * 60 * 60 * 24)) + 1)

      if (habit.frequency === 'daily') {
        totalPossibleCompletions += daysInWeek
      } else if (habit.frequency === 'weekly') {
        totalPossibleCompletions += 1
      }

      // Count actual completions this week
      for (let i = 0; i < daysInWeek; i++) {
        const checkDate = new Date(weekStart)
        checkDate.setDate(weekStart.getDate() + i)
        const dateStr = checkDate.toISOString().split('T')[0]

        const record = habitRecords.find(r => {
          try {
            const recordDateStr = typeof r.date === 'string' ? r.date : new Date(r.date).toISOString().split('T')[0]
            return r.habitId === habit.id && recordDateStr === dateStr && (r.completed || (r.value && r.value > 0))
          } catch {
            return false
          }
        })

        if (record) {
          if (habit.frequency === 'daily') {
            actualCompletions += 1
          } else if (habit.frequency === 'weekly' && i === 0) {
            actualCompletions += 1
          }
        }
      }
    })

    return totalPossibleCompletions > 0 ? Math.round((actualCompletions / totalPossibleCompletions) * 100) : 0
  }

  // Calculate overall habit completion rate
  const overallHabitCompletionRate = useMemo(() => {
    if (areas.length === 0) return 0
    const rates = areas.map(area => getAreaHabitCompletionRate(area.id))
    return Math.round(rates.reduce((sum, rate) => sum + rate, 0) / rates.length)
  }, [areas, habits, habitRecords])

  // Get habit streak for display
  const getHabitStreakDays = (areaId: string) => {
    const areaHabits = habits.filter(h => h.areaId === areaId)
    if (areaHabits.length === 0) return 0

    // Simple calculation: count consecutive days from today backwards
    let streak = 0
    const today = new Date()

    for (let i = 0; i < 7; i++) {
      const checkDate = new Date(today)
      checkDate.setDate(today.getDate() - i)
      const dateStr = checkDate.toISOString().split('T')[0]

      let dayCompleted = false
      for (const habit of areaHabits) {
        const record = habitRecords.find(r => {
          try {
            const recordDateStr = typeof r.date === 'string' ? r.date : new Date(r.date).toISOString().split('T')[0]
            return r.habitId === habit.id && recordDateStr === dateStr && (r.completed || (r.value && r.value > 0))
          } catch {
            return false
          }
        })
        if (record) {
          dayCompleted = true
          break
        }
      }

      if (dayCompleted) {
        streak++
      } else {
        break
      }
    }

    return streak
  }



  return (
    <>
    <div className="h-full bg-gradient-to-br from-background to-muted/20">
      <div className="p-4 md:p-6 lg:p-8 w-full max-w-none h-full overflow-y-auto scrollbar-hide">
        {/* Header & Quick Capture */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold text-foreground">
              {getTimeGreeting()}，{userSettings.username || '用户'}！ 🌟
            </h1>
            <p className="text-muted-foreground mt-1">{getCurrentDateString()}</p>
          </div>
          <div className="relative mt-4 md:mt-0 w-full md:w-96">
            <div className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
              <span className="text-lg">💡</span>
            </div>
            <input
              type="text"
              value={quickInputContent}
              onChange={handleQuickInputChange}
              onKeyDown={handleQuickInputKeyDown}
              placeholder="记录想法(输入#添加标签，Shift+Enter提交)"
              className="w-full bg-card border border-border rounded-lg pl-10 pr-4 py-2.5 text-foreground focus:outline-none focus:ring-2 focus:ring-primary transition"
              disabled={isSubmittingQuickInput}
            />

            {/* 标签建议下拉框 */}
            {showTagSuggestions && (
              <div className="absolute top-full left-0 right-0 mt-1 bg-card border border-border rounded-lg shadow-lg z-50 max-h-40 overflow-y-auto scrollbar-hide">
                {commonTags
                  .filter(tag => tag.toLowerCase().includes(currentTagInput.toLowerCase()))
                  .map((tag, index) => (
                    <div
                      key={tag}
                      className={`px-3 py-2 cursor-pointer text-sm ${
                        index === tagSuggestionIndex ? 'bg-accent text-accent-foreground' : 'hover:bg-accent/50'
                      }`}
                      onClick={() => handleTagSelect(tag)}
                    >
                      #{tag}
                    </div>
                  ))}
                {currentTagInput && !commonTags.some(tag => tag.toLowerCase().includes(currentTagInput.toLowerCase())) && (
                  <div
                    className={`px-3 py-2 cursor-pointer text-sm ${
                      tagSuggestionIndex === commonTags.filter(tag => tag.toLowerCase().includes(currentTagInput.toLowerCase())).length
                        ? 'bg-accent text-accent-foreground' : 'hover:bg-accent/50'
                    }`}
                    onClick={() => handleTagSelect(currentTagInput)}
                  >
                    #{currentTagInput} <span className="text-muted-foreground">(新标签)</span>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Dashboard Grid - 三列布局 */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">

          {/* Left Column - 占据2列 */}
          <div className="lg:col-span-2 space-y-6">

            {/* Review Prompt */}
            {isWeeklyReviewDue() && (
              <Card className="bg-gradient-to-r from-purple-50 to-pink-50 border-purple-200">
                <CardContent className="p-5">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="bg-purple-100 p-3 rounded-full">
                        <Calendar className="w-6 h-6 text-purple-600" />
                      </div>
                      <div className="ml-4">
                        <h3 className="text-lg font-semibold text-gray-900">是时候进行你的周复盘了!</h3>
                        <p className="text-purple-700 text-sm">让 AI 帮你总结过去一周，开启新的一周。</p>
                      </div>
                    </div>
                    <Button
                      onClick={handleStartReview}
                      className="bg-purple-600 hover:bg-purple-500 text-white font-semibold px-4 py-2 rounded-lg whitespace-nowrap flex items-center"
                    >
                      ✨ AI 智能复盘
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Focus & Action */}
            <Card className="bg-card/50 border border-border/50 shadow-lg">
              <CardHeader className="p-5 border-b border-border">
                <CardTitle className="text-xl font-bold text-foreground flex items-center">
                  <Target className="w-6 h-6 mr-3 text-orange-400" />
                  行动焦点
                </CardTitle>
                <div className="mt-4">
                  <Tabs defaultValue="today" className="w-full">
                    <TabsList className="grid w-full grid-cols-2 bg-muted/50 rounded-lg p-1">
                      <TabsTrigger
                        value="today"
                        className="data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm text-muted-foreground font-medium text-sm rounded-md transition-all"
                      >
                        今日待办
                      </TabsTrigger>
                      <TabsTrigger
                        value="upcoming"
                        className="data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm text-muted-foreground font-medium text-sm rounded-md transition-all"
                      >
                        即将到期
                      </TabsTrigger>
                    </TabsList>
                    <TabsContent value="today" className="mt-4">
                      <div className="space-y-4 max-h-[400px] overflow-y-auto scrollbar-hide">
                        <TodayTasks />
                      </div>
                    </TabsContent>
                    <TabsContent value="upcoming" className="mt-4">
                      <div className="space-y-4 max-h-[400px] overflow-y-auto scrollbar-hide">
                        <UpcomingProjects />
                      </div>
                    </TabsContent>
                  </Tabs>
                </div>
              </CardHeader>
            </Card>

            {/* Project Radar */}
            <div>
              <h2 className="text-xl font-bold text-foreground mb-4 flex items-center">
                <TrendingUp className="w-6 h-6 mr-3 text-cyan-400" />
                项目雷达
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {projects.slice(0, 4).map((project) => {
                  const statusClasses = {
                    'Completed': 'bg-green-500/20 text-green-300',
                    'In Progress': 'bg-blue-500/20 text-blue-300',
                    'On Hold': 'bg-yellow-500/20 text-yellow-300',
                    'Cancelled': 'bg-red-500/20 text-red-300',
                    'Not Started': 'bg-gray-500/20 text-gray-300',
                    'At Risk': 'bg-orange-500/20 text-orange-300',
                    'Paused': 'bg-purple-500/20 text-purple-300'
                  }
                  const progressColor = {
                    'Completed': 'bg-green-500',
                    'In Progress': 'bg-blue-500',
                    'On Hold': 'bg-yellow-500',
                    'Cancelled': 'bg-red-500',
                    'Not Started': 'bg-gray-500',
                    'At Risk': 'bg-orange-500',
                    'Paused': 'bg-purple-500'
                  }

                  // 状态中文翻译
                  const getStatusText = (status: string) => {
                    const statusMap: Record<string, string> = {
                      'Completed': '已完成',
                      'In Progress': '进行中',
                      'On Hold': '暂停',
                      'Cancelled': '已取消',
                      'Not Started': '未开始',
                      'At Risk': '有风险',
                      'Paused': '已暂停'
                    }
                    return statusMap[status] || status
                  }
                  const projectTasks = tasks.filter(t => t.projectId === project.id)
                  const completedTasks = projectTasks.filter(t => t.completed).length
                  const progress = projectTasks.length > 0 ? Math.round((completedTasks / projectTasks.length) * 100) : 0

                  return (
                    <Card key={project.id} className="bg-card/50 border border-border/50 cursor-pointer hover:bg-accent/50 transition-colors"
                          onClick={() => navigate(`/projects/${project.id}`)}>
                      <CardContent className="p-4">
                        <div className="flex justify-between items-start mb-4">
                          <div>
                            <p className="text-xs text-muted-foreground">
                              {project.areaId ? areas.find(a => a.id === project.areaId)?.name || '未分类' : '未分类'}
                            </p>
                            <h3 className="font-bold text-foreground">{project.name}</h3>
                          </div>
                          <Badge className={`text-xs font-bold px-2 py-1 ${statusClasses[project.status] || 'bg-gray-500/20 text-gray-300'}`}>
                            {getStatusText(project.status)}
                          </Badge>
                        </div>
                        <div className="space-y-2">
                          <div className="flex justify-between text-xs text-muted-foreground">
                            <span>进度</span>
                            <span>{progress}%</span>
                          </div>
                          <Progress value={progress} className="h-2" />
                          <div className="flex justify-between items-center text-xs text-muted-foreground border-t border-border pt-3">
                            <span className="flex items-center">
                              <span className="w-3 h-3 rounded-full bg-green-500 mr-1"></span>
                              {completedTasks}/{projectTasks.length} 任务
                            </span>
                            <Button variant="ghost" size="sm" className="text-primary hover:text-primary/80 font-semibold h-auto p-0">
                              查看详情
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )
                })}
              </div>
            </div>
          </div>
          {/* Right Column */}
          <div className="space-y-6">
            {/* Areas & Habits */}
            <div>
              <h2 className="text-xl font-bold text-foreground mb-4 flex items-center">
                <Users className="w-6 h-6 mr-3 text-emerald-400" />
                领域与习惯
              </h2>
              <div className="space-y-4">
                {areas.slice(0, 2).map((area) => {
                  const completionRate = getAreaHabitCompletionRate(area.id)
                  const areaHabits = habits.filter(h => h.areaId === area.id)

                  return (
                    <Card key={area.id} className="bg-card/50 border border-border/50 cursor-pointer hover:bg-accent/50 transition-colors"
                          onClick={() => navigate(`/areas/${area.id}`)}>
                      <CardContent className="p-4">
                        <h3 className="font-bold text-foreground">{area.name}</h3>
                        <p className="text-xs text-muted-foreground italic mt-1 line-clamp-2">
                          "{area.standard || '暂无标准'}"
                        </p>
                        <div className="my-4 space-y-2">
                          {areaHabits.slice(0, 2).map((habit) => {
                            // 计算最近7天的记录，修正日期偏移
                            const last7Days = Array.from({ length: 7 }, (_, i) => {
                              const date = new Date()
                              date.setDate(date.getDate() - i - 1) // 往前推一天修正偏移
                              return date.toISOString().split('T')[0]
                            }).reverse()

                            const records: { completed: boolean; progress: number }[] = last7Days.map(dateStr => {
                              const record = habitRecords.find(r => {
                                try {
                                  const recordDateStr = typeof r.date === 'string' ? r.date : new Date(r.date).toISOString().split('T')[0]
                                  return r.habitId === habit.id && recordDateStr === dateStr
                                } catch {
                                  return false
                                }
                              })

                              if (isNumericHabit(habit)) {
                                // 数值型习惯
                                const value = record?.value ?? 0
                                const target = habit.target || 1
                                const progress = Math.min((value / target) * 100, 100)
                                return {
                                  completed: progress >= 100,
                                  progress
                                }
                              } else {
                                // 布尔型习惯
                                return {
                                  completed: record?.completed || false,
                                  progress: record?.completed ? 100 : 0
                                }
                              }
                            })

                            return (
                              <div key={habit.id} className="flex justify-between items-center text-sm">
                                <span className="text-muted-foreground">{habit.name}</span>
                                <div className="flex space-x-1.5">
                                  {records.map((record, index) => {
                                    // 根据新的日期计算逻辑，最后一个圆点（index=6）是今天
                                    const isToday = index === 6

                                    // 根据进度确定颜色
                                    let bgColor = 'bg-muted' // 默认灰色（未完成）
                                    if (record.progress >= 100) {
                                      bgColor = 'bg-emerald-500' // 绿色（完成）
                                    } else if (record.progress > 0) {
                                      bgColor = 'bg-blue-500' // 蓝色（部分完成）
                                    }

                                    return (
                                      <span
                                        key={index}
                                        className={`w-3 h-3 rounded-full relative ${bgColor} ${
                                          isToday ? 'ring-2 ring-blue-400 ring-opacity-70 shadow-lg' : ''
                                        }`}
                                        title={isToday ? '今日' : ''}
                                      >
                                        {isToday && (
                                          <span className="absolute inset-0 rounded-full border-2 border-blue-400 animate-pulse"></span>
                                        )}
                                      </span>
                                    )
                                  })}
                                </div>
                              </div>
                            )
                          })}
                        </div>
                        <div className="flex justify-around items-center border-t border-border pt-3">
                          <div className="text-center">
                            <p className="text-lg font-bold text-foreground">{completionRate}%</p>
                            <p className="text-xs text-muted-foreground">完成率</p>
                          </div>
                          <div className="text-center">
                            <p className="text-lg font-bold text-foreground">{areaHabits.length}</p>
                            <p className="text-xs text-muted-foreground">习惯数</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )
                })}

                {areas.length === 0 && (
                  <Card className="bg-card/50 border border-border/50">
                    <CardContent className="p-4 text-center">
                      <div className="text-4xl mb-2">🎯</div>
                      <p className="text-sm text-muted-foreground">暂无领域数据</p>
                      <p className="text-xs text-muted-foreground mt-1">创建领域开始追踪习惯吧！</p>
                    </CardContent>
                  </Card>
                )}
              </div>
            </div>

            {/* Inbox & Recently Active */}
            <div>
              <h2 className="text-xl font-bold text-foreground mb-4 flex items-center">
                <Clock className="w-6 h-6 mr-3 text-rose-400" />
                快速访问
              </h2>
              <Card className="bg-card/50 border border-border/50 shadow-lg">
                <CardContent className="p-5 space-y-4">
                  {/* Inbox */}
                  <div className="flex justify-between items-center">
                    <span className="text-muted-foreground font-medium">收件箱</span>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-primary hover:text-primary/80 font-semibold h-auto p-0"
                      onClick={() => navigate('/inbox')}
                    >
                      处理
                      <Badge className="bg-primary/20 text-primary text-xs font-bold ml-2 px-2 py-0.5">
                        {inboxItems}
                      </Badge>
                      <span className="ml-1">→</span>
                    </Button>
                  </div>

                  {/* Recently Active */}
                  <div>
                    <h3 className="text-muted-foreground font-medium mb-2">最近活跃</h3>
                    <ul className="space-y-2 text-sm">
                      {projects.slice(0, 2).map((project) => (
                        <li key={project.id} className="flex items-center cursor-pointer hover:text-foreground transition-colors"
                            onClick={() => navigate(`/projects/${project.id}`)}>
                          <Target className="w-4 h-4 mr-2 text-cyan-400" />
                          <span className="truncate">项目: {project.name}</span>
                        </li>
                      ))}
                      {areas.slice(0, 1).map((area) => (
                        <li key={area.id} className="flex items-center cursor-pointer hover:text-foreground transition-colors"
                            onClick={() => navigate(`/areas/${area.id}`)}>
                          <Users className="w-4 h-4 mr-2 text-emerald-400" />
                          <span className="truncate">领域: {area.name}</span>
                        </li>
                      ))}
                      <li className="flex items-center cursor-pointer hover:text-foreground transition-colors"
                          onClick={() => navigate('/resources')}>
                        <BookOpen className="w-4 h-4 mr-2 text-muted-foreground" />
                        <span className="truncate">资源: 查看知识库</span>
                      </li>
                    </ul>
                  </div>

                  {/* Quick Actions */}
                  <div className="border-t border-border pt-4">
                    <h3 className="text-muted-foreground font-medium mb-2">快速操作</h3>
                    <div className="grid grid-cols-2 gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        className="h-8 text-xs"
                        onClick={() => setIsCreateProjectDialogOpen(true)}
                      >
                        <Target className="w-3 h-3 mr-1" />
                        新建项目
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="h-8 text-xs"
                        onClick={() => setIsCreateAreaDialogOpen(true)}
                      >
                        <Users className="w-3 h-3 mr-1" />
                        新建领域
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="h-8 text-xs"
                        onClick={() => addNotification({
                          type: 'info',
                          title: '功能开发中',
                          message: '新建文件功能正在开发中'
                        })}
                      >
                        <BookOpen className="w-3 h-3 mr-1" />
                        新建文件
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="h-8 text-xs"
                        onClick={() => navigate('/reviews')}
                      >
                        <Calendar className="w-3 h-3 mr-1" />
                        复盘回顾
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>

    {/* 对话框 */}
    <CreateProjectDialog
      isOpen={isCreateProjectDialogOpen}
      onClose={() => setIsCreateProjectDialogOpen(false)}
      onSubmit={handleCreateProject}
    />

    <CreateAreaDialog
      isOpen={isCreateAreaDialogOpen}
      onClose={() => setIsCreateAreaDialogOpen(false)}
      onSubmit={handleCreateArea}
    />
  </>
  )
}

export default DashboardPage
