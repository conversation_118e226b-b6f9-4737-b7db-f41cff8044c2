/**
 * 统一引用服务
 * 整合 WikiLink、项目引用、领域引用的管理
 */

export interface UnifiedReference {
  id: string
  sourceType: 'document' | 'project' | 'area'
  sourceId: string
  sourcePath?: string
  sourceTitle: string
  targetType: 'document'
  targetPath: string
  targetTitle: string
  referenceType: 'wikilink' | 'description' | 'task' | 'note'
  context: string
  strength: number // 引用强度 0-1
  createdAt: string
  updatedAt: string
}

export interface WikiLinkReference extends UnifiedReference {
  sourceType: 'document'
  referenceType: 'wikilink'
  linkText: string
  displayText?: string
  startPosition: number
  endPosition: number
  lineNumber: number
  columnNumber: number
}

export interface ProjectReference extends UnifiedReference {
  sourceType: 'project'
  referenceType: 'description' | 'task' | 'note'
  projectId: string
  taskId?: string
}

export interface AreaReference extends UnifiedReference {
  sourceType: 'area'
  referenceType: 'description' | 'note'
  areaId: string
}

export interface ReferenceStatistics {
  totalReferences: number
  wikiLinks: number
  projectReferences: number
  areaReferences: number
  incomingReferences: number
  outgoingReferences: number
  strongestReference: number
  weakestReference: number
}

export interface ReferenceCache {
  documentPath: string
  references: UnifiedReference[]
  statistics: ReferenceStatistics
  lastUpdated: string
  version: number
}

/**
 * 统一引用服务类
 */
export class UnifiedReferenceService {
  private cache = new Map<string, ReferenceCache>()
  private cacheTimeout = 5 * 60 * 1000 // 5分钟缓存
  private pendingQueries = new Map<string, Promise<UnifiedReference[]>>() // 防重复查询

  /**
   * 获取文档的所有引用
   */
  async getAllReferences(documentPath: string): Promise<UnifiedReference[]> {
    console.log('🔍 获取文档的所有引用:', documentPath)

    // 检查缓存
    const cached = this.getCachedReferences(documentPath)
    if (cached) {
      console.log('📋 [getAllReferences] 使用缓存的引用数据')
      return cached.references
    }

    // 检查是否有正在进行的查询
    const pendingQuery = this.pendingQueries.get(documentPath)
    if (pendingQuery) {
      console.log('⏳ [getAllReferences] 等待正在进行的查询完成')
      return await pendingQuery
    }

    console.log('🔄 [getAllReferences] 缓存未命中，开始新查询')

    // 创建查询Promise并存储
    const queryPromise = this.performQuery(documentPath)
    this.pendingQueries.set(documentPath, queryPromise)

    try {
      const result = await queryPromise
      return result
    } finally {
      // 查询完成后清理
      this.pendingQueries.delete(documentPath)
    }
  }

  /**
   * 执行实际的引用查询
   */
  private async performQuery(documentPath: string): Promise<UnifiedReference[]> {
    // 并行获取各种类型的引用
    const [wikiLinks, projectRefs, areaRefs, resourceLinkRefs] = await Promise.all([
      this.getWikiLinkReferences(documentPath),
      this.getProjectReferences(documentPath),
      this.getAreaReferences(documentPath),
      this.getResourceLinkReferences(documentPath)
    ])

    const allReferences = [...wikiLinks, ...projectRefs, ...areaRefs, ...resourceLinkRefs]
    
    // 更新缓存
    this.updateCache(documentPath, allReferences)

    console.log('✅ [getAllReferences] 获取到统一引用:', {
      总数: allReferences.length,
      WikiLink: wikiLinks.length,
      项目引用: projectRefs.length,
      领域引用: areaRefs.length,
      ResourceLink引用: resourceLinkRefs.length,
      详细引用: allReferences.map(ref => ({
        id: ref.id,
        类型: ref.referenceType,
        源: ref.sourceTitle,
        目标: ref.targetTitle
      }))
    })

    return allReferences
  }

  /**
   * 获取 WikiLink 引用
   */
  private async getWikiLinkReferences(documentPath: string): Promise<WikiLinkReference[]> {
    try {
      // 调用现有的双向链接服务
      const { bidirectionalLinkService } = await import('./bidirectionalLinkService')
      const linkData = await bidirectionalLinkService.getDocumentLinks(documentPath)

      // 合并反向链接和出链
      const allLinks = [...linkData.backlinks, ...linkData.outlinks]

      console.log('🔍 [getWikiLinkReferences] WikiLink数据详情:', {
        反向链接数: linkData.backlinks.length,
        出链数: linkData.outlinks.length,
        总链接数: allLinks.length,
        链接详情: allLinks.map(link => ({
          id: link.id,
          sourceDocPath: link.sourceDocPath,
          targetDocPath: link.targetDocPath,
          linkText: link.linkText,
          linkType: link.linkType
        }))
      })

      return allLinks.map(link => ({
        id: `wikilink-${link.id}`,
        sourceType: 'document' as const,
        sourceId: link.sourceDocPath,
        sourcePath: link.sourceDocPath,
        sourceTitle: link.sourceDocTitle || '',
        targetType: 'document' as const,
        targetPath: link.targetDocPath,
        targetTitle: link.targetDocTitle || '',
        referenceType: 'wikilink' as const,
        context: `${link.contextBefore}${link.linkText}${link.contextAfter}`,
        strength: link.linkStrength || 1.0,
        createdAt: link.createdAt || new Date().toISOString(),
        updatedAt: link.updatedAt || new Date().toISOString(),
        linkText: link.linkText || '',
        displayText: link.displayText,
        startPosition: link.startPosition || 0,
        endPosition: link.endPosition || 0,
        lineNumber: link.lineNumber || 0,
        columnNumber: link.columnNumber || 0
      }))
    } catch (error) {
      console.error('❌ 获取 WikiLink 引用失败:', error)
      return []
    }
  }

  /**
   * 获取项目引用
   * 查找当前文档中的项目引用（出链）
   */
  private async getProjectReferences(documentPath: string): Promise<ProjectReference[]> {
    try {
      console.log('🔍 查询项目引用:', documentPath)

      // 使用引用解析器解析文档内容中的项目引用
      const { referenceParser } = await import('./referenceParser')

      // 读取文档内容
      const { fileSystemApi } = await import('../lib/api')
      const result = await fileSystemApi.readFile({ path: documentPath })

      if (!result.success || !result.data?.content) {
        console.log('📄 无法读取文档内容，跳过项目引用解析')
        return []
      }

      const content = result.data.content
      console.log('📄 读取到文档内容长度:', content.length)
      console.log('📄 文档内容预览:', content.substring(0, 200))

      const parsedReferences = referenceParser.parseAllReferences(content, documentPath)
      console.log('🔍 解析到的所有引用:', parsedReferences.length, parsedReferences)

      // 过滤出项目引用，这些是当前文档引用的项目
      const projectRefs = parsedReferences.filter(ref => ref.type === 'project')
      console.log('📋 过滤出的项目引用:', projectRefs.length, projectRefs)

      return projectRefs.map(ref => {
        // 判断是否为任务引用
        const isTaskRef = ref.target.includes('任务') || ref.target.includes('task') ||
                         content.substring(Math.max(0, ref.startIndex - 10), ref.startIndex).includes('@task:')

        return {
          id: `project-${ref.startIndex}-${ref.target}`,
          sourceType: 'document' as const, // 修正：源是当前文档
          sourceId: documentPath,
          sourcePath: documentPath,
          sourceTitle: documentPath.split('/').pop()?.replace('.md', '') || documentPath,
          targetType: 'document' as const, // 目标是项目（这里简化处理）
          targetPath: `projects/${ref.target}`,
          targetTitle: ref.target,
          referenceType: isTaskRef ? 'task' as const : 'description' as const,
          context: ref.context,
          strength: ref.strength,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          projectId: ref.target,
          taskId: isTaskRef ? ref.target : undefined
        }
      })
    } catch (error) {
      console.error('❌ 获取项目引用失败:', error)
      return []
    }
  }

  /**
   * 获取领域引用
   * 查找当前文档中的领域引用（出链）
   */
  private async getAreaReferences(documentPath: string): Promise<AreaReference[]> {
    try {
      console.log('🔍 查询领域引用:', documentPath)

      // 使用引用解析器解析文档内容中的领域引用
      const { referenceParser } = await import('./referenceParser')

      // 读取文档内容
      const { fileSystemApi } = await import('../lib/api')
      const result = await fileSystemApi.readFile({ path: documentPath })

      if (!result.success || !result.data?.content) {
        console.log('📄 无法读取文档内容，跳过领域引用解析')
        return []
      }

      const content = result.data.content
      console.log('📄 领域引用 - 读取到文档内容长度:', content.length)

      const parsedReferences = referenceParser.parseAllReferences(content, documentPath)
      console.log('🔍 领域引用 - 解析到的所有引用:', parsedReferences.length, parsedReferences)

      // 过滤出领域引用，这些是当前文档引用的领域
      const areaRefs = parsedReferences.filter(ref => ref.type === 'area')
      console.log('🏷️ 过滤出的领域引用:', areaRefs.length, areaRefs)

      return areaRefs.map(ref => ({
        id: `area-${ref.startIndex}-${ref.target}`,
        sourceType: 'document' as const, // 修正：源是当前文档
        sourceId: documentPath,
        sourcePath: documentPath,
        sourceTitle: documentPath.split('/').pop()?.replace('.md', '') || documentPath,
        targetType: 'document' as const, // 目标是领域（这里简化处理）
        targetPath: `areas/${ref.target}`,
        targetTitle: ref.target,
        referenceType: 'note' as const,
        context: ref.context,
        strength: ref.strength,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        areaId: ref.target
      }))
    } catch (error) {
      console.error('❌ 获取领域引用失败:', error)
      return []
    }
  }

  /**
   * 获取ResourceLink表中的反向引用
   * 查找哪些项目和领域通过ResourceLink表关联了当前资源
   */
  private async getResourceLinkReferences(documentPath: string): Promise<UnifiedReference[]> {
    try {
      console.log('🔍 查询ResourceLink反向引用:', documentPath)

      // 导入API和工具函数
      const { databaseApi } = await import('../lib/api')
      const { getResourcesPath } = await import('../plugins/wikilink/utils')

      // 将虚拟路径转换为真实路径（ResourceLink表中存储的是绝对路径）
      let realPath: string
      try {
        // 获取用户设置
        const { useUserSettingsStore } = await import('../store/userSettingsStore')
        const settings = useUserSettingsStore.getState().settings

        // 判断是否为绝对路径
        // Windows绝对路径：C:\ 或 D:\
        // Linux/Mac绝对路径：/home/<USER>/Users/<USER>
        // 虚拟路径：/filename.md 或 filename.md (单个文件名)
        const isWindowsAbsolutePath = /^[A-Za-z]:[\\\/]/.test(documentPath)
        const isUnixAbsolutePath = documentPath.startsWith('/') &&
          documentPath.split('/').filter(part => part.length > 0).length > 1 // 至少有两个非空路径部分
        const isAbsolutePath = isWindowsAbsolutePath || isUnixAbsolutePath

        if (isAbsolutePath) {
          // 已经是绝对路径，直接使用
          realPath = documentPath
        } else {
          // 虚拟路径，需要转换为绝对路径
          const resourcesPath = await getResourcesPath(settings)
          const relativePath = documentPath.replace(/^\/+/, '') // 移除开头的斜杠
          realPath = `${resourcesPath}/${relativePath}`.replace(/\\/g, '/') // 统一使用正斜杠
        }

        console.log('🔍 [getResourceLinkReferences] 路径转换:', {
          原始路径: documentPath,
          是否Windows绝对路径: isWindowsAbsolutePath,
          是否Unix绝对路径: isUnixAbsolutePath,
          是否绝对路径: isAbsolutePath,
          转换后路径: realPath,
          资源根目录: await getResourcesPath(settings)
        })
      } catch (error) {
        console.error('❌ 路径转换失败:', error)
        realPath = documentPath
      }

      // 查询ResourceLink表中的反向引用
      // 尝试多种路径格式以确保匹配
      const pathVariants = [
        realPath,
        realPath.replace(/\//g, '\\'), // 正斜杠转反斜杠
        realPath.replace(/\\/g, '/'),  // 反斜杠转正斜杠
      ]

      console.log('🔍 [getResourceLinkReferences] 尝试多种路径格式:', pathVariants)

      let result
      for (const pathVariant of pathVariants) {
        result = await databaseApi.getResourceReferences(pathVariant)
        if (result.success && result.data &&
            (result.data.projects.length > 0 || result.data.areas.length > 0)) {
          console.log('✅ [getResourceLinkReferences] 找到匹配路径:', pathVariant)
          break
        }
      }

      if (!result.success || !result.data) {
        console.log('📄 无法获取ResourceLink引用数据，尝试其他路径格式')

        // 如果第一次查询失败，尝试其他路径格式
        let alternativeResult
        if (isAbsolutePath) {
          // 如果原来是绝对路径，尝试相对路径
          const resourcesPath = await getResourcesPath(settings)
          const relativePath = documentPath.replace(resourcesPath, '').replace(/^\/+/, '')
          console.log('🔄 尝试相对路径查询:', relativePath)
          alternativeResult = await databaseApi.getResourceReferences(relativePath)
        } else {
          // 如果原来是相对路径，尝试原始路径
          console.log('🔄 尝试原始路径查询:', documentPath)
          alternativeResult = await databaseApi.getResourceReferences(documentPath)
        }

        if (!alternativeResult?.success || !alternativeResult?.data) {
          console.log('📄 所有路径格式都无法找到ResourceLink引用数据')
          return []
        }

        // 使用备选结果
        result.data = alternativeResult.data
      }

      const { projects, areas } = result.data
      const references: UnifiedReference[] = []

      // 处理项目引用
      projects.forEach((project, index) => {
        references.push({
          id: `resourcelink-project-${project.id}`,
          sourceType: 'project' as const,
          sourceId: project.id,
          sourcePath: `projects/${project.id}`,
          sourceTitle: project.name,
          targetType: 'document' as const,
          targetPath: documentPath,
          targetTitle: documentPath.split('/').pop()?.replace('.md', '') || documentPath,
          referenceType: 'description' as const,
          context: `项目 "${project.name}" 关联了此资源`,
          strength: 1.0,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          projectId: project.id
        })
      })

      // 处理领域引用
      areas.forEach((area, index) => {
        references.push({
          id: `resourcelink-area-${area.id}`,
          sourceType: 'area' as const,
          sourceId: area.id,
          sourcePath: `areas/${area.id}`,
          sourceTitle: area.name,
          targetType: 'document' as const,
          targetPath: documentPath,
          targetTitle: documentPath.split('/').pop()?.replace('.md', '') || documentPath,
          referenceType: 'note' as const,
          context: `领域 "${area.name}" 关联了此资源`,
          strength: 1.0,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          areaId: area.id
        })
      })

      console.log('✅ ResourceLink反向引用查询完成:', {
        查询路径: realPath,
        项目引用: projects.length,
        领域引用: areas.length,
        总引用: references.length,
        项目详情: projects.map(p => ({ id: p.id, name: p.name })),
        领域详情: areas.map(a => ({ id: a.id, name: a.name }))
      })

      return references
    } catch (error) {
      console.error('❌ 获取ResourceLink引用失败:', error)
      return []
    }
  }

  /**
   * 计算引用统计
   */
  calculateStatistics(references: UnifiedReference[]): ReferenceStatistics {
    const wikiLinks = references.filter(ref => ref.referenceType === 'wikilink').length

    // 项目引用：包括 task 和 description 类型
    const projectReferences = references.filter(ref =>
      ref.referenceType === 'task' || ref.referenceType === 'description'
    ).length

    // 领域引用：note 类型
    const areaReferences = references.filter(ref => ref.referenceType === 'note').length

    const strengths = references.map(ref => ref.strength).filter(s => s > 0)

    console.log('📊 引用统计计算:', {
      总数: references.length,
      WikiLink: wikiLinks,
      项目引用: projectReferences,
      领域引用: areaReferences,
      引用详情: references.map(ref => ({
        类型: ref.referenceType,
        源类型: ref.sourceType,
        目标: ref.targetTitle,
        ID: ref.id
      }))
    })

    return {
      totalReferences: references.length,
      wikiLinks,
      projectReferences,
      areaReferences,
      incomingReferences: references.length, // 简化实现
      outgoingReferences: 0, // 需要额外查询
      strongestReference: strengths.length > 0 ? Math.max(...strengths) : 0,
      weakestReference: strengths.length > 0 ? Math.min(...strengths) : 0
    }
  }

  /**
   * 获取缓存的引用
   */
  private getCachedReferences(documentPath: string): ReferenceCache | null {
    const cached = this.cache.get(documentPath)
    if (!cached) {
      console.log('📋 [缓存] 无缓存数据:', documentPath)
      return null
    }

    const now = Date.now()
    const cacheAge = now - new Date(cached.lastUpdated).getTime()

    if (cacheAge > this.cacheTimeout) {
      console.log('📋 [缓存] 缓存已过期，清理:', { 文档: documentPath, 缓存年龄: Math.round(cacheAge / 1000) + 's' })
      this.cache.delete(documentPath)
      return null
    }

    console.log('📋 [缓存] 使用有效缓存:', { 文档: documentPath, 缓存年龄: Math.round(cacheAge / 1000) + 's' })
    return cached
  }

  /**
   * 更新缓存
   */
  private updateCache(documentPath: string, references: UnifiedReference[]): void {
    const statistics = this.calculateStatistics(references)
    
    const cacheEntry: ReferenceCache = {
      documentPath,
      references,
      statistics,
      lastUpdated: new Date().toISOString(),
      version: 1
    }

    this.cache.set(documentPath, cacheEntry)
    console.log('💾 缓存已更新:', documentPath)
  }

  /**
   * 清理缓存
   */
  clearCache(documentPath?: string): void {
    if (documentPath) {
      this.cache.delete(documentPath)
      console.log('🗑️ 清理指定文档缓存:', documentPath)
    } else {
      this.cache.clear()
      console.log('🗑️ 清理所有缓存')
    }
  }

  /**
   * 清理指定文档的缓存
   */
  clearCache(documentPath?: string): void {
    if (documentPath) {
      console.log('🗑️ [缓存] 清理指定文档缓存:', documentPath)
      this.cache.delete(documentPath)
    } else {
      console.log('🗑️ [缓存] 清理所有缓存')
      this.cache.clear()
    }
  }

  /**
   * 获取缓存统计
   */
  getCacheStats(): { size: number; documents: string[] } {
    return {
      size: this.cache.size,
      documents: Array.from(this.cache.keys())
    }
  }
}

// 导出单例实例
export const unifiedReferenceService = new UnifiedReferenceService()
