import React, { useState, useEffect } from 'react'
import { UnifiedReference, unifiedReferenceService } from '../../services/unifiedReferenceService'
import { ReferenceStrengthChart } from './ReferenceStrengthIndicator'
import ReferenceNetworkVisualization from './ReferenceNetworkVisualization'
import ReferenceQuickActions from './ReferenceQuickActions'

interface ReferenceAnalyticsDashboardProps {
  documentPath: string
  onNavigate?: (path: string) => void
  className?: string
}

interface AnalyticsData {
  references: UnifiedReference[]
  trends: {
    daily: { date: string; count: number }[]
    weekly: { week: string; count: number }[]
    monthly: { month: string; count: number }[]
  }
  topReferences: {
    mostReferenced: UnifiedReference[]
    strongestReferences: UnifiedReference[]
    recentReferences: UnifiedReference[]
  }
  insights: {
    growthRate: number
    diversityIndex: number
    networkHealth: number
    recommendations: string[]
  }
}

/**
 * 引用分析仪表板
 * 提供全面的引用数据分析和可视化
 */
export const ReferenceAnalyticsDashboard: React.FC<ReferenceAnalyticsDashboardProps> = ({
  documentPath,
  onNavigate,
  className = ''
}) => {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null)
  const [loading, setLoading] = useState(false)
  const [activeView, setActiveView] = useState<'overview' | 'network' | 'trends' | 'insights'>('overview')

  // 加载分析数据
  useEffect(() => {
    loadAnalyticsData()
  }, [documentPath])

  const loadAnalyticsData = async () => {
    setLoading(true)
    try {
      console.log('📊 加载引用分析数据...')
      
      // 获取引用数据
      const references = await unifiedReferenceService.getAllReferences(documentPath)
      
      // 生成模拟的趋势数据
      const trends = generateTrendData(references)
      
      // 分析顶级引用
      const topReferences = analyzeTopReferences(references)
      
      // 生成洞察
      const insights = generateInsights(references)
      
      setAnalyticsData({
        references,
        trends,
        topReferences,
        insights
      })
      
      console.log('✅ 分析数据加载完成')
    } catch (error) {
      console.error('❌ 加载分析数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 生成趋势数据（基于真实数据）
  const generateTrendData = (references: UnifiedReference[]) => {
    const now = new Date()
    const daily = []
    const weekly = []
    const monthly = []

    // 按创建时间分组引用
    const referencesByDate = new Map<string, number>()
    references.forEach(ref => {
      if (ref.createdAt) {
        const date = new Date(ref.createdAt).toISOString().split('T')[0]
        referencesByDate.set(date, (referencesByDate.get(date) || 0) + 1)
      }
    })

    // 生成过去7天的数据
    for (let i = 6; i >= 0; i--) {
      const date = new Date(now)
      date.setDate(date.getDate() - i)
      const dateStr = date.toISOString().split('T')[0]
      daily.push({
        date: dateStr,
        count: referencesByDate.get(dateStr) || 0
      })
    }

    // 生成过去4周的数据
    for (let i = 3; i >= 0; i--) {
      const weekStart = new Date(now)
      weekStart.setDate(weekStart.getDate() - i * 7 - 6)
      const weekEnd = new Date(now)
      weekEnd.setDate(weekEnd.getDate() - i * 7)

      let weekCount = 0
      for (let d = new Date(weekStart); d <= weekEnd; d.setDate(d.getDate() + 1)) {
        const dateStr = d.toISOString().split('T')[0]
        weekCount += referencesByDate.get(dateStr) || 0
      }

      weekly.push({
        week: `第${4-i}周`,
        count: weekCount
      })
    }

    // 生成过去6个月的数据
    for (let i = 5; i >= 0; i--) {
      const monthStart = new Date(now.getFullYear(), now.getMonth() - i, 1)
      const monthEnd = new Date(now.getFullYear(), now.getMonth() - i + 1, 0)

      let monthCount = 0
      for (let d = new Date(monthStart); d <= monthEnd; d.setDate(d.getDate() + 1)) {
        const dateStr = d.toISOString().split('T')[0]
        monthCount += referencesByDate.get(dateStr) || 0
      }

      monthly.push({
        month: monthStart.toLocaleDateString('zh-CN', { month: 'short' }),
        count: monthCount
      })
    }

    return { daily, weekly, monthly }
  }

  // 分析顶级引用
  const analyzeTopReferences = (references: UnifiedReference[]) => {
    // 按强度排序
    const strongestReferences = [...references]
      .sort((a, b) => b.strength - a.strength)
      .slice(0, 5)

    // 按创建时间排序（最新的）
    const recentReferences = [...references]
      .filter(ref => ref.createdAt) // 确保有创建时间
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      .slice(0, 5)

    // 按引用类型和强度综合排序（热门引用）
    const mostReferenced = [...references]
      .map(ref => ({
        ...ref,
        score: ref.strength * (ref.referenceType === 'wikilink' ? 1.2 : 1.0) // WikiLink权重稍高
      }))
      .sort((a, b) => b.score - a.score)
      .slice(0, 5)

    return {
      mostReferenced,
      strongestReferences,
      recentReferences
    }
  }

  // 生成洞察
  const generateInsights = (references: UnifiedReference[]) => {
    const wikiLinks = references.filter(ref => ref.referenceType === 'wikilink').length
    const projectRefs = references.filter(ref => ref.referenceType === 'task' || ref.referenceType === 'description').length
    const areaRefs = references.filter(ref => ref.referenceType === 'note').length
    
    const total = references.length
    const avgStrength = total > 0 ? references.reduce((sum, ref) => sum + ref.strength, 0) / total : 0
    
    // 计算多样性指数（基于引用类型分布）
    const diversityIndex = total > 0 ? 
      1 - ((wikiLinks/total)**2 + (projectRefs/total)**2 + (areaRefs/total)**2) : 0
    
    // 网络健康度（基于平均强度和多样性）
    const networkHealth = (avgStrength + diversityIndex) / 2
    
    // 生成建议
    const recommendations = []
    if (wikiLinks < total * 0.3) {
      recommendations.push('考虑增加更多文档间的 WikiLink 连接')
    }
    if (projectRefs === 0) {
      recommendations.push('尝试添加项目引用来跟踪相关任务')
    }
    if (areaRefs === 0) {
      recommendations.push('使用领域标签来分类和组织内容')
    }
    if (avgStrength < 0.5) {
      recommendations.push('提高引用质量，增加上下文信息')
    }
    if (recommendations.length === 0) {
      recommendations.push('引用结构良好，继续保持！')
    }

    // 计算真实增长率（基于最近7天vs前7天的引用数量）
    const now = new Date()
    const recentWeek = references.filter(ref => {
      if (!ref.createdAt) return false
      const refDate = new Date(ref.createdAt)
      const daysAgo = (now.getTime() - refDate.getTime()) / (1000 * 60 * 60 * 24)
      return daysAgo <= 7
    }).length

    const previousWeek = references.filter(ref => {
      if (!ref.createdAt) return false
      const refDate = new Date(ref.createdAt)
      const daysAgo = (now.getTime() - refDate.getTime()) / (1000 * 60 * 60 * 24)
      return daysAgo > 7 && daysAgo <= 14
    }).length

    const growthRate = previousWeek > 0 ? ((recentWeek - previousWeek) / previousWeek) * 100 : 0

    return {
      growthRate,
      diversityIndex,
      networkHealth,
      recommendations
    }
  }

  // 处理快捷操作
  const handleQuickAction = {
    refresh: loadAnalyticsData,
    export: () => {
      console.log('📤 导出分析报告...')
      // TODO: 实现导出功能
    },
    createReference: (type: 'wikilink' | 'project' | 'area') => {
      console.log('➕ 创建引用:', type)
      // TODO: 实现创建引用功能
    }
  }

  if (loading) {
    return (
      <div className={`reference-analytics-dashboard ${className}`}>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"></div>
            <div className="text-gray-500">加载分析数据...</div>
          </div>
        </div>
      </div>
    )
  }

  if (!analyticsData) {
    return (
      <div className={`reference-analytics-dashboard ${className}`}>
        <div className="text-center text-gray-500 py-8">
          <div className="text-4xl mb-4">📊</div>
          <div>暂无分析数据</div>
        </div>
      </div>
    )
  }

  return (
    <div className={`reference-analytics-dashboard ${className}`}>
      {/* 标签页导航 */}
      <div className="dashboard-tabs border-b mb-6">
        <div className="flex space-x-1">
          {[
            { key: 'overview', label: '概览', icon: '📊' },
            { key: 'network', label: '网络图', icon: '🕸️' },
            { key: 'trends', label: '趋势', icon: '📈' },
            { key: 'insights', label: '洞察', icon: '💡' }
          ].map(tab => (
            <button
              key={tab.key}
              type="button"
              onClick={() => setActiveView(tab.key as any)}
              className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
                activeView === tab.key
                  ? 'border-blue-500 text-blue-600 bg-blue-50'
                  : 'border-transparent text-gray-600 hover:text-gray-800 hover:bg-gray-50'
              }`}
            >
              <span className="mr-2">{tab.icon}</span>
              {tab.label}
            </button>
          ))}
        </div>
      </div>

      {/* 概览视图 */}
      {activeView === 'overview' && (
        <div className="overview-view space-y-6">
          {/* 关键指标 */}
          <div className="key-metrics grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="metric-card bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="text-2xl font-bold text-blue-600">{analyticsData.references.length}</div>
              <div className="text-sm text-blue-500">总引用数</div>
            </div>
            <div className="metric-card bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="text-2xl font-bold text-green-600">{analyticsData.insights.growthRate.toFixed(1)}%</div>
              <div className="text-sm text-green-500">增长率</div>
            </div>
            <div className="metric-card bg-purple-50 border border-purple-200 rounded-lg p-4">
              <div className="text-2xl font-bold text-purple-600">{(analyticsData.insights.diversityIndex * 100).toFixed(0)}%</div>
              <div className="text-sm text-purple-500">多样性指数</div>
            </div>
            <div className="metric-card bg-orange-50 border border-orange-200 rounded-lg p-4">
              <div className="text-2xl font-bold text-orange-600">{(analyticsData.insights.networkHealth * 100).toFixed(0)}%</div>
              <div className="text-sm text-orange-500">网络健康度</div>
            </div>
          </div>

          {/* 强度分布图表 */}
          <div className="strength-chart bg-white border rounded-lg p-4">
            <h3 className="font-medium text-gray-800 mb-4">引用强度分布</h3>
            <ReferenceStrengthChart 
              strengths={analyticsData.references.map(ref => ref.strength)}
            />
          </div>

          {/* 快捷操作 */}
          <ReferenceQuickActions
            documentPath={documentPath}
            references={analyticsData.references}
            onRefresh={handleQuickAction.refresh}
            onExport={handleQuickAction.export}
            onCreateReference={handleQuickAction.createReference}
          />
        </div>
      )}

      {/* 网络图视图 */}
      {activeView === 'network' && (
        <div className="network-view">
          <ReferenceNetworkVisualization
            references={analyticsData.references}
            onNodeClick={(nodeId) => {
              console.log('🔗 点击网络节点:', nodeId)
              onNavigate?.(nodeId)
            }}
          />
        </div>
      )}

      {/* 趋势视图 */}
      {activeView === 'trends' && (
        <div className="trends-view space-y-6">
          <div className="trend-charts grid grid-cols-1 lg:grid-cols-3 gap-3">
            {/* 日趋势 */}
            <div className="trend-chart bg-white border rounded-lg p-3">
              <h3 className="font-medium text-gray-800 mb-3 text-sm truncate">日趋势</h3>
              <div className="space-y-2">
                {analyticsData.trends.daily.map((item, index) => (
                  <div key={index} className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">{item.date}</span>
                    <div className="flex items-center space-x-2">
                      <div
                        className="bg-blue-500 h-2 rounded"
                        style={{ width: `${Math.max(4, Math.min(100, (item.count / Math.max(1, Math.max(...analyticsData.trends.daily.map(d => d.count)))) * 100))}px` }}
                      ></div>
                      <span className="font-medium">{item.count}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* 周趋势 */}
            <div className="trend-chart bg-white border rounded-lg p-3">
              <h3 className="font-medium text-gray-800 mb-3 text-sm truncate">周趋势</h3>
              <div className="space-y-2">
                {analyticsData.trends.weekly.map((item, index) => (
                  <div key={index} className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">{item.week}</span>
                    <div className="flex items-center space-x-2">
                      <div
                        className="bg-green-500 h-2 rounded"
                        style={{ width: `${Math.max(4, Math.min(100, (item.count / Math.max(1, Math.max(...analyticsData.trends.weekly.map(w => w.count)))) * 100))}px` }}
                      ></div>
                      <span className="font-medium">{item.count}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* 月趋势 */}
            <div className="trend-chart bg-white border rounded-lg p-3">
              <h3 className="font-medium text-gray-800 mb-3 text-sm truncate">月趋势</h3>
              <div className="space-y-2">
                {analyticsData.trends.monthly.map((item, index) => (
                  <div key={index} className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">{item.month}</span>
                    <div className="flex items-center space-x-2">
                      <div 
                        className="bg-purple-500 h-2 rounded"
                        style={{ width: `${(item.count / 20) * 100}px` }}
                      ></div>
                      <span className="font-medium">{item.count}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 洞察视图 */}
      {activeView === 'insights' && (
        <div className="insights-view space-y-6">
          {/* 智能建议 */}
          <div className="recommendations bg-white border rounded-lg p-4">
            <h3 className="font-medium text-gray-800 mb-4">💡 智能建议</h3>
            <div className="space-y-3">
              {analyticsData.insights.recommendations.map((rec, index) => (
                <div key={index} className="flex items-start space-x-3 p-3 bg-blue-50 rounded-lg">
                  <div className="text-blue-500 mt-0.5">💡</div>
                  <div className="text-sm text-blue-800">{rec}</div>
                </div>
              ))}
            </div>
          </div>

          {/* 顶级引用 */}
          <div className="top-references grid grid-cols-1 lg:grid-cols-3 gap-3">
            <div className="strongest-refs bg-white border rounded-lg p-3">
              <h3 className="font-medium text-gray-800 mb-3 text-sm truncate">🔥 最强引用</h3>
              <div className="space-y-2">
                {analyticsData.topReferences.strongestReferences.map((ref, index) => (
                  <div key={index} className="text-sm p-2 bg-gray-50 rounded">
                    <div className="font-medium truncate">{ref.targetTitle}</div>
                    <div className="text-gray-500">强度: {(ref.strength * 100).toFixed(0)}%</div>
                  </div>
                ))}
              </div>
            </div>

            <div className="recent-refs bg-white border rounded-lg p-3">
              <h3 className="font-medium text-gray-800 mb-3 text-sm truncate">🆕 最新引用</h3>
              <div className="space-y-2">
                {analyticsData.topReferences.recentReferences.map((ref, index) => (
                  <div key={index} className="text-sm p-2 bg-gray-50 rounded">
                    <div className="font-medium truncate">{ref.targetTitle}</div>
                    <div className="text-gray-500">{new Date(ref.createdAt).toLocaleDateString()}</div>
                  </div>
                ))}
              </div>
            </div>

            <div className="most-refs bg-white border rounded-lg p-3">
              <h3 className="font-medium text-gray-800 mb-3 text-sm truncate">📈 热门引用</h3>
              <div className="space-y-2">
                {analyticsData.topReferences.mostReferenced.map((ref, index) => (
                  <div key={index} className="text-sm p-2 bg-gray-50 rounded">
                    <div className="font-medium truncate">{ref.targetTitle}</div>
                    <div className="text-gray-500">{ref.referenceType}</div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default ReferenceAnalyticsDashboard
