import { NavLink, useLocation, useNavigate } from 'react-router-dom'
import { useState, useEffect } from 'react'
import { cn } from '../../lib/utils'
import { Badge } from '../ui/badge'
import { Button } from '../ui/button'
import { routes, type RouteCategory } from '../../lib/router'
import { useLanguage } from '../../contexts/LanguageContext'
import { useNavigationStore } from '../../store/navigationStore'
import { useUserStore } from '../../store/userStore'

// Icon components (simplified SVG icons)
const Icons = {
  chevronLeft: () => (
    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
    </svg>
  ),
  chevronRight: () => (
    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
    </svg>
  ),
  dashboard: () => (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"
      />
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"
      />
    </svg>
  ),
  inbox: () => (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"
      />
    </svg>
  ),

  project: () => (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
      />
    </svg>
  ),
  area: () => (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
      />
    </svg>
  ),
  resource: () => (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
      />
    </svg>
  ),
  archive: () => (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4"
      />
    </svg>
  ),
  review: () => (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
      />
    </svg>
  ),
  settings: () => (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
      />
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
      />
    </svg>
  ),
  sun: () => (
    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
    </svg>
  ),
  moon: () => (
    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
    </svg>
  ),
  logout: () => (
    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
    </svg>
  ),
  user: () => (
    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
    </svg>
  )
}

// 将在组件内部使用翻译函数动态获取

// 分类方案重构：聚焦 PARA 主流程 + 工作流 + 系统
const categoryColors: Record<RouteCategory, string> = {
  main: 'default',
  para: 'secondary',
  tools: 'outline'
}

export function Navigation() {
  const location = useLocation()
  const navigate = useNavigate()
  const { t } = useLanguage()
  const { isCollapsed, toggleCollapsed } = useNavigationStore()
  const { user, toggleTheme, logout } = useUserStore()
  const [showUserMenu, setShowUserMenu] = useState(false)

  // 点击外部区域关闭菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (showUserMenu && !(event.target as Element).closest('.user-menu-container')) {
        setShowUserMenu(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [showUserMenu])

  // 处理菜单项点击
  const handleMenuClick = (action: string) => {
    setShowUserMenu(false)

    switch (action) {
      case 'profile':
        // 跳转到个人资料页面（如果存在）
        console.log('打开个人资料')
        break
      case 'settings':
        navigate('/settings')
        break
      case 'theme':
        toggleTheme()
        break
      case 'logout':
        logout()
        break
    }
  }

  // 获取主题图标
  const getThemeIcon = () => {
    if (!user) return <Icons.sun />

    switch (user.theme) {
      case 'light':
        return <Icons.sun />
      case 'dark':
        return <Icons.moon />
      default:
        return <Icons.sun />
    }
  }

  // 动态获取分类标签
  const getCategoryLabel = (category: RouteCategory): string => {
    return t(`categories.${category}`)
  }

  // Group routes by category
  const routesByCategory = routes.reduce(
    (acc, route) => {
      if (!acc[route.category]) {
        acc[route.category] = [] as Array<(typeof routes)[number]>
      }
      acc[route.category].push(route)
      return acc
    },
    {} as Record<RouteCategory, Array<(typeof routes)[number]>>
  )

  // PARA 优先的展示顺序
  const orderedCategories: RouteCategory[] = ['para', 'main', 'tools']
  const entries = orderedCategories
    .filter((c) => routesByCategory[c] && routesByCategory[c].length)
    .map((c) => [c, routesByCategory[c]] as const)

  // 移除侧边栏搜索交互逻辑

  return (
    <>
      <nav className={cn(
        "flex flex-col h-full bg-sidebar text-sidebar-foreground border-r border-sidebar-border transition-all duration-300",
        isCollapsed ? "w-16" : "w-64"
      )}>
        {/* Logo/Brand 与收缩按钮 */}
        <div className="p-4 border-b border-sidebar-border flex items-center justify-between">
          {!isCollapsed && <h1 className="text-lg font-bold">PaoLife</h1>}
          <Button
            variant="ghost"
            size="sm"
            onClick={toggleCollapsed}
            className="p-1 h-8 w-8"
          >
            {isCollapsed ? <Icons.chevronRight /> : <Icons.chevronLeft />}
          </Button>
        </div>

        {/* Navigation Links - 单一部分，按指定顺序 */}
        <div className="flex-1 overflow-y-auto p-4">
          <div className="space-y-1">
            {routes.map((route) => {
              const isActive = location.pathname === route.path || location.pathname.startsWith(route.path + '/')
              const IconComponent = Icons[route.icon as keyof typeof Icons]

              return (
                <NavLink
                  key={route.path}
                  to={route.path}
                  className={({ isActive: navIsActive }) =>
                    cn(
                      'flex items-center rounded-lg text-sm font-medium transition-all duration-200',
                      'focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
                      'hover:bg-muted/50 hover:text-foreground hover:shadow-sm',
                      navIsActive || isActive
                        ? 'bg-primary text-primary-foreground'
                        : 'text-sidebar-foreground',
                      isCollapsed ? 'justify-center px-3 py-2' : 'gap-3 px-3 py-2'
                    )
                  }
                >
                  {IconComponent && (
                    <span className="flex-shrink-0">
                      <IconComponent />
                    </span>
                  )}
                  {!isCollapsed && (
                    <span className="font-medium">{t(`nav.${route.path.slice(1)}`)}</span>
                  )}
                </NavLink>
              )
            })}
          </div>
        </div>

        {/* 用户信息区域 */}
        <div className="mt-auto p-4 border-t border-sidebar-border">
          {isCollapsed ? (
            // 收缩状态：头像 + 主题切换按钮
            <div className="space-y-2">
              {/* 用户头像 */}
              <div className="relative user-menu-container">
                <button
                  type="button"
                  className="w-full flex justify-center hover:bg-muted/50 rounded-lg p-1 transition-colors"
                  title="用户设置"
                  onClick={() => setShowUserMenu(!showUserMenu)}
                >
                  <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center relative">
                    <Icons.user />
                    {/* 在线状态指示器 */}
                    {user?.isOnline && (
                      <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 rounded-full border-2 border-sidebar"></div>
                    )}
                  </div>
                </button>

                {/* 用户菜单下拉 */}
                {showUserMenu && (
                  <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 w-48 bg-background border border-border rounded-lg shadow-lg py-1 z-50">
                    <button
                      type="button"
                      className="w-full px-3 py-2 text-left text-sm hover:bg-muted flex items-center gap-2"
                      onClick={() => handleMenuClick('profile')}
                    >
                      <Icons.user />
                      个人资料
                    </button>
                    <button
                      type="button"
                      className="w-full px-3 py-2 text-left text-sm hover:bg-muted flex items-center gap-2"
                      onClick={() => handleMenuClick('settings')}
                    >
                      <Icons.settings />
                      设置
                    </button>
                    <hr className="my-1 border-border" />
                    <button
                      type="button"
                      className="w-full px-3 py-2 text-left text-sm hover:bg-muted text-red-600 flex items-center gap-2"
                      onClick={() => handleMenuClick('logout')}
                    >
                      <Icons.logout />
                      退出登录
                    </button>
                  </div>
                )}
              </div>

              {/* 主题切换按钮 */}
              <button
                type="button"
                className="w-full flex justify-center hover:bg-muted/50 rounded-lg p-1 transition-colors"
                title={`切换主题 (当前: ${user?.theme || 'system'})`}
                onClick={() => handleMenuClick('theme')}
              >
                <div className="w-8 h-8 flex items-center justify-center">
                  {getThemeIcon()}
                </div>
              </button>
            </div>
          ) : (
            // 展开状态：显示完整用户信息
            <div className="relative user-menu-container">
              <button
                type="button"
                className="w-full flex items-center gap-3 hover:bg-muted/50 rounded-lg p-2 transition-colors text-left"
                onClick={() => setShowUserMenu(!showUserMenu)}
              >
                <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0 relative">
                  <Icons.user />
                  {/* 在线状态指示器 */}
                  {user?.isOnline && (
                    <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 rounded-full border-2 border-sidebar"></div>
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-sidebar-foreground truncate">
                    {user?.username || '未登录'}
                  </p>
                  <p className="text-xs text-muted-foreground truncate">
                    {user?.email || ''}
                  </p>
                </div>
                <svg className="w-4 h-4 text-muted-foreground flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </button>

              {/* 用户菜单下拉 */}
              {showUserMenu && (
                <div className="absolute bottom-full left-0 right-0 mb-2 bg-background border border-border rounded-lg shadow-lg py-1 z-50">
                  <button
                    type="button"
                    className="w-full px-3 py-2 text-left text-sm hover:bg-muted flex items-center gap-2"
                    onClick={() => handleMenuClick('profile')}
                  >
                    <Icons.user />
                    个人资料
                  </button>
                  <button
                    type="button"
                    className="w-full px-3 py-2 text-left text-sm hover:bg-muted flex items-center gap-2"
                    onClick={() => handleMenuClick('settings')}
                  >
                    <Icons.settings />
                    设置
                  </button>
                  <button
                    type="button"
                    className="w-full px-3 py-2 text-left text-sm hover:bg-muted flex items-center gap-2"
                    onClick={() => handleMenuClick('theme')}
                  >
                    {getThemeIcon()}
                    切换主题 ({user?.theme || 'system'})
                  </button>
                  <hr className="my-1 border-border" />
                  <button
                    type="button"
                    className="w-full px-3 py-2 text-left text-sm hover:bg-muted text-red-600 flex items-center gap-2"
                    onClick={() => handleMenuClick('logout')}
                  >
                    <Icons.logout />
                    退出登录
                  </button>
                </div>
              )}
            </div>
          )}
        </div>
      </nav>

      {/* 全局搜索已移除 */}
    </>
  )
}

export default Navigation
