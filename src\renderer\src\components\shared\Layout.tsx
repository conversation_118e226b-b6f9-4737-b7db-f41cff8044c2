import { Outlet, useLocation, useParams } from 'react-router-dom'
import { useEffect, useState } from 'react'
import Navigation from './Navigation'
import Breadcrumbs from './Breadcrumbs'
import { ErrorBoundary } from './ErrorBoundary'
import { ToastContainer } from '../ui/toast'
import { useUIStore } from '../../store/uiStore'
import { useNavigationStore } from '../../store/navigationStore'
import { cn } from '../../lib/utils'


export function Layout() {
  const location = useLocation()
  const params = useParams()
  const [isTransitioning, setIsTransitioning] = useState(false)
  const { notifications, removeNotification } = useUIStore()
  const { isCollapsed } = useNavigationStore()

  // {{ AURA-X: Add - 获取当前页面的项目或领域ID. Approval: 寸止(ID:1738157400). }}
  const projectId = params.projectId
  const areaId = params.areaId

  // Handle page transitions - disabled to prevent flashing
  // useEffect(() => {
  //   setIsTransitioning(true)
  //   const timer = setTimeout(() => setIsTransitioning(false), 150)
  //   return () => clearTimeout(timer)
  // }, [location.pathname])

  return (
    <div className="flex h-screen bg-background">
      {/* Left Sidebar Navigation */}
      <aside className={cn(
        "sidebar-layout flex-shrink-0 scrollbar-hidden overflow-y-auto transition-all duration-300",
        isCollapsed ? "w-16" : "w-64"
      )}>
        <Navigation />
      </aside>

      {/* Main Content Area */}
      <main className="flex-1 flex flex-col overflow-hidden">
        {/* Top Bar with Breadcrumbs */}
        <header className="border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 drag-region">
          <div className="flex items-center justify-between px-6 py-3">
            <Breadcrumbs />

            {/* Window Controls (for Electron) - Fluent Design Style */}
            <div className="flex items-center no-drag">
              {/* 最小化按钮 */}
              <button
                type="button"
                onClick={() => window.electronAPI?.window.minimize()}
                className="window-control-btn"
                title="最小化"
                aria-label="Minimize"
              >
                <svg fill="currentColor" viewBox="0 0 16 16">
                  <path d="M3 8h10v1H3z" />
                </svg>
              </button>

              {/* 最大化/还原按钮 */}
              <button
                type="button"
                onClick={() => window.electronAPI?.window.maximize()}
                className="window-control-btn"
                title="最大化"
                aria-label="Maximize"
              >
                <svg fill="currentColor" viewBox="0 0 16 16">
                  <path d="M3 3v10h10V3H3zm9 9H4V4h8v8z" />
                </svg>
              </button>

              {/* 关闭按钮 */}
              <button
                type="button"
                onClick={() => window.electronAPI?.window.close()}
                className="window-control-btn close"
                title="关闭"
                aria-label="Close"
              >
                <svg fill="currentColor" viewBox="0 0 16 16">
                  <path d="M8 7.293l2.146-2.147.708.708L8.707 8l2.147 2.146-.708.708L8 8.707l-2.146 2.147-.708-.708L7.293 8 5.146 5.854l.708-.708L8 7.293z" />
                </svg>
              </button>
            </div>
          </div>
        </header>

        {/* Page Content */}
        <div className="flex-1 overflow-hidden">
          <div className="h-full overflow-y-auto scrollbar-hidden">
            <ErrorBoundary>
              <div className="opacity-100 transform translate-y-0">
                <Outlet />
              </div>
            </ErrorBoundary>
          </div>
        </div>
      </main>

      {/* Toast Notifications */}
      <ToastContainer notifications={notifications} onRemove={removeNotification} />


    </div>
  )
}

export default Layout
