import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'
import type { Area, AreaMetric, Habit, HabitRecord } from '../../../shared/types'
import { databaseApi } from '../lib/api'
import { databaseApi } from '../lib/api'

export interface AreaState {
  areas: Area[]
  currentArea: Area | null
  habits: Habit[]
  habitRecords: HabitRecord[]
  metrics: AreaMetric[]
  loading: boolean
  error: string | null
}

export interface AreaActions {
  // Area CRUD operations
  setAreas: (areas: Area[]) => void
  addArea: (area: Area) => void
  updateArea: (id: string, updates: Partial<Area>) => void
  deleteArea: (id: string) => void
  archiveArea: (id: string) => Promise<void>

  // Current area management
  setCurrentArea: (area: Area | null) => void

  // Habit management
  setHabits: (habits: Habit[]) => void
  addHabit: (habit: Habit) => void
  updateHabit: (id: string, updates: Partial<Habit>) => void
  deleteHabit: (id: string) => Promise<void>

  // Habit record management
  setHabitRecords: (records: HabitRecord[]) => void
  addHabitRecord: (record: HabitRecord) => void
  updateHabitRecord: (id: string, updates: Partial<HabitRecord>) => void
  toggleHabitRecord: (habitId: string, date: Date) => Promise<void>
  setHabitValue: (habitId: string, date: Date, value: number) => Promise<void>

  // Metrics management
  setMetrics: (metrics: AreaMetric[]) => void
  addMetric: (metric: AreaMetric) => void
  updateMetric: (id: string, updates: Partial<AreaMetric>) => void
  deleteMetric: (id: string) => void

  // Loading and error states
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void

  // Async operations
  fetchAreas: () => Promise<void>
  createArea: (data: Omit<Area, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>
  fetchHabitsForArea: (areaId: string) => Promise<void>
  fetchAllHabitsAndRecords: () => Promise<void>
}

export type AreaStore = AreaState & AreaActions

// 清理无效日期的辅助函数
const cleanHabitRecords = (records: HabitRecord[]): HabitRecord[] => {
  return records.filter(record => {
    try {
      // 验证日期是否有效
      if (typeof record.date === 'string') {
        new Date(record.date).toISOString()
      } else {
        new Date(record.date).toISOString()
      }
      return true
    } catch (error) {
      console.warn('Removing invalid habit record with bad date:', record)
      return false
    }
  })
}

export const useAreaStore = create<AreaStore>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        areas: [],
        currentArea: null,
        habits: [],
        habitRecords: [],
        metrics: [],
        loading: false,
        error: null,



        // Area actions
        setAreas: (areas) => set({ areas }),

        addArea: (area) =>
          set((state) => ({
            areas: [area, ...state.areas]
          })),

        // {{ AURA-X: Modify - 统一编辑入口：乐观更新 + 数据库存储，避免领域编辑只改内存. Confirmed via 寸止 }}
        updateArea: (id, updates) => {
          // 1) 乐观更新本地
          set((state) => ({
            areas: state.areas.map((area) => (area.id === id ? { ...area, ...updates } : area)),
            currentArea:
              state.currentArea?.id === id
                ? { ...state.currentArea, ...updates }
                : state.currentArea
          }))

          // 2) 持久化到数据库
          databaseApi
            .updateArea({ id, updates: updates as any })
            .then((result) => {
              if (!result.success) {
                console.error('Failed to persist area updates:', result.error)
              } else if (result.data) {
                // 与后端对齐一次
                set((state) => ({
                  areas: state.areas.map((a) => (a.id === id ? { ...a, ...result.data } : a)),
                  currentArea:
                    state.currentArea?.id === id
                      ? { ...state.currentArea, ...result.data }
                      : state.currentArea
                }))
              }
            })
            .catch((err) => console.error('Error persisting area updates:', err))
        },

        deleteArea: (id) =>
          set((state) => ({
            areas: state.areas.filter((area) => area.id !== id),
            currentArea: state.currentArea?.id === id ? null : state.currentArea
          })),

        archiveArea: async (id) => {
          try {
            const result = await databaseApi.archiveArea(id)
            if (result.success) {
              // 改为标记 archived:true，避免丢失其它字段
              set((state) => ({
                areas: state.areas.map((area) => (area.id === id ? { ...area, archived: true, updatedAt: new Date() } : area))
              }))
              console.log('Area archived successfully')
            } else {
              console.error('Failed to archive area:', result.error)
            }
          } catch (error) {
            console.error('Error archiving area:', error)
          }
        },

        setCurrentArea: (area) => set({ currentArea: area }),

        // Habit actions
        setHabits: (habits) => set({ habits }),

        addHabit: (habit) =>
          set((state) => ({
            habits: [habit, ...state.habits]
          })),

        updateHabit: (id, updates) =>
          set((state) => ({
            habits: state.habits.map((habit) =>
              habit.id === id ? { ...habit, ...updates } : habit
            )
          })),

        deleteHabit: async (id) => {
          try {
            // 先从数据库删除
            const result = await databaseApi.deleteHabit(id)
            if (result.success) {
              // 数据库删除成功后，从本地状态删除
              set((state) => ({
                habits: state.habits.filter((habit) => habit.id !== id),
                // 同时删除相关的习惯记录
                habitRecords: state.habitRecords.filter((record) => record.habitId !== id)
              }))
            } else {
              console.error('Failed to delete habit from database:', result.error)
            }
          } catch (error) {
            console.error('Error deleting habit:', error)
          }
        },

        // Habit record actions
        setHabitRecords: (records) => set({ habitRecords: cleanHabitRecords(records) }),

        addHabitRecord: (record) => {
          try {
            // 验证日期是否有效
            if (typeof record.date === 'string') {
              new Date(record.date).toISOString()
            } else {
              new Date(record.date).toISOString()
            }
            set((state) => ({
              habitRecords: [record, ...state.habitRecords]
            }))
          } catch (error) {
            console.error('Cannot add habit record with invalid date:', record)
          }
        },

        updateHabitRecord: (id, updates) =>
          set((state) => ({
            habitRecords: state.habitRecords.map((record) =>
              record.id === id ? { ...record, ...updates } : record
            )
          })),

        toggleHabitRecord: async (habitId, date) => {
          const dateStr = date.toISOString().split('T')[0]
          const existingRecord = get().habitRecords.find(
            (record) => {
              try {
                const recordDateStr = typeof record.date === 'string'
                  ? record.date
                  : new Date(record.date).toISOString().split('T')[0]
                return record.habitId === habitId && recordDateStr === dateStr
              } catch (error) {
                console.warn('Invalid date in habit record:', record.date)
                return false
              }
            }
          )

          try {
            if (existingRecord) {
              // Update existing record in database
              const newCompleted = !existingRecord.completed
              const result = await databaseApi.updateHabitRecord({
                id: existingRecord.id,
                updates: {
                  completed: newCompleted
                }
              })

              if (result.success) {
                // Update local state
                get().updateHabitRecord(existingRecord.id, {
                  completed: newCompleted
                })
                // 触发习惯记录变化事件
                document.dispatchEvent(new CustomEvent('habit-record-changed'))
              } else {
                console.error('Failed to update habit record:', result.error)
              }
            } else {
              // Create new record in database
              const result = await databaseApi.createHabitRecord({
                habitId,
                date,
                completed: true
              })

              if (result.success) {
                // Add to local state with real ID from database
                const newRecord: HabitRecord = {
                  id: result.data.id,
                  habitId,
                  date,
                  completed: true
                }
                get().addHabitRecord(newRecord)
                // 触发习惯记录变化事件
                document.dispatchEvent(new CustomEvent('habit-record-changed'))
              } else {
                console.error('Failed to create habit record:', result.error)
              }
            }
          } catch (error) {
            console.error('Error toggling habit record:', error)
          }
        },

        setHabitValue: async (habitId, date, value) => {
          const dateStr = date.toISOString().split('T')[0]
          const existingRecord = get().habitRecords.find(
            (record) => {
              try {
                const recordDateStr = typeof record.date === 'string'
                  ? record.date
                  : new Date(record.date).toISOString().split('T')[0]
                return record.habitId === habitId && recordDateStr === dateStr
              } catch (error) {
                console.warn('Invalid date in habit record:', record.date)
                return false
              }
            }
          )

          try {
            if (existingRecord) {
              // Update existing record in database
              const result = await databaseApi.updateHabitRecord({
                id: existingRecord.id,
                updates: {
                  value,
                  completed: value > 0
                }
              })

              if (result.success) {
                // Update local state
                get().updateHabitRecord(existingRecord.id, {
                  value,
                  completed: value > 0
                })
                // 触发习惯记录变化事件
                document.dispatchEvent(new CustomEvent('habit-record-changed'))
              } else {
                console.error('Failed to update habit record:', result.error)
              }
            } else {
              // Create new record in database
              const result = await databaseApi.createHabitRecord({
                habitId,
                date,
                value,
                completed: value > 0
              })

              if (result.success) {
                // Add to local state with real ID from database
                const newRecord: HabitRecord = {
                  id: result.data.id,
                  habitId,
                  date,
                  completed: value > 0,
                  value
                }
                get().addHabitRecord(newRecord)
                // 触发习惯记录变化事件
                document.dispatchEvent(new CustomEvent('habit-record-changed'))
              } else {
                console.error('Failed to create habit record:', result.error)
              }
            }
          } catch (error) {
            console.error('Error saving habit value:', error)
          }
        },

        // Metrics actions
        setMetrics: (metrics) => set({ metrics }),

        addMetric: (metric) =>
          set((state) => ({
            metrics: [metric, ...state.metrics]
          })),

        updateMetric: (id, updates) =>
          set((state) => ({
            metrics: state.metrics.map((metric) =>
              metric.id === id ? { ...metric, ...updates } : metric
            )
          })),

        deleteMetric: (id) =>
          set((state) => ({
            metrics: state.metrics.filter((metric) => metric.id !== id)
          })),

        // Common actions
        setLoading: (loading) => set({ loading }),
        setError: (error) => set({ error }),

        // Async operations
        fetchAreas: async () => {
          set({ loading: true, error: null })
          try {
            const result = await databaseApi.getAreas()
            if (result.success) {
              // Convert database areas to frontend Area type
              const areas: Area[] =
                result.data?.map((dbArea: any) => ({
                  id: dbArea.id,
                  name: dbArea.name,
                  description: dbArea.description,
                  standard: dbArea.standard,
                  icon: dbArea.icon,
                  color: dbArea.color,
                  status: dbArea.status || 'Active',
                  reviewFrequency: dbArea.reviewFrequency || 'Weekly',
                  archived: dbArea.archived || false,
                  createdAt: new Date(dbArea.createdAt),
                  updatedAt: new Date(dbArea.updatedAt)
                })) || []
              set({ areas, loading: false })
            } else {
              set({ error: result.error, loading: false })
            }
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : 'Failed to fetch areas',
              loading: false
            })
          }
        },

        createArea: async (_data) => {
          set({ loading: true, error: null })
          try {
            // Will be implemented with IPC
            set({ loading: false })
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : 'Failed to create area',
              loading: false
            })
          }
        },

        fetchHabitsForArea: async (areaId) => {
          set({ loading: true, error: null })
          try {
            const result = await databaseApi.habits.getByArea(areaId)
            if (result.success) {
              // Convert database habits to frontend Habit type
              const habits: Habit[] =
                result.data?.map((dbHabit: any) => {
                  try {
                    return {
                      id: dbHabit.id,
                      name: dbHabit.name,
                      description: dbHabit.description,
                      areaId: dbHabit.areaId,
                      frequency: dbHabit.frequency || 'daily',
                      target: dbHabit.target || 1,
                      archived: dbHabit.archived || false,
                      createdAt: dbHabit.createdAt ? new Date(dbHabit.createdAt) : new Date(),
                      updatedAt: dbHabit.updatedAt ? new Date(dbHabit.updatedAt) : new Date()
                    }
                  } catch (error) {
                    console.error('Error converting habit from database:', dbHabit, error)
                    return {
                      id: dbHabit.id,
                      name: dbHabit.name || 'Unknown Habit',
                      description: dbHabit.description,
                      areaId: dbHabit.areaId,
                      frequency: 'daily',
                      target: 1,
                      archived: false,
                      createdAt: new Date(),
                      updatedAt: new Date() // 使用当前时间作为默认值
                    }
                  }
                }).filter(Boolean) || []
              set({ habits, loading: false })
            } else {
              set({ error: result.error, loading: false })
            }
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : 'Failed to fetch habits',
              loading: false
            })
          }
        },

        fetchAllHabitsAndRecords: async () => {
          set({ loading: true, error: null })
          try {
            const areas = get().areas.filter(area => !area.archived)
            const allHabits: Habit[] = []
            const allRecords: HabitRecord[] = []

            console.log(`Loading habits for ${areas.length} areas`)

            // 为每个领域加载习惯和记录
            for (const area of areas) {
              try {
                // 加载习惯
                console.log(`Loading habits for area: ${area.name} (${area.id})`)
                const habitsResult = await databaseApi.habits.getByArea(area.id)
                if (habitsResult.success && habitsResult.data) {
                  console.log(`Found ${habitsResult.data.length} habits for area ${area.name}`)
                  const habits: Habit[] = habitsResult.data.map((dbHabit: any) => {
                    try {
                      return {
                        id: dbHabit.id,
                        name: dbHabit.name,
                        description: dbHabit.description,
                        areaId: dbHabit.areaId,
                        frequency: dbHabit.frequency || 'daily',
                        target: dbHabit.target || 1,
                        archived: dbHabit.archived || false,
                        createdAt: dbHabit.createdAt ? new Date(dbHabit.createdAt) : new Date(),
                        updatedAt: dbHabit.updatedAt ? new Date(dbHabit.updatedAt) : new Date()
                      }
                    } catch (error) {
                      console.error('Error converting habit from database:', dbHabit, error)
                      return {
                        id: dbHabit.id,
                        name: dbHabit.name || 'Unknown Habit',
                        description: dbHabit.description,
                        areaId: dbHabit.areaId,
                        frequency: 'daily',
                        target: 1,
                        archived: false,
                        createdAt: new Date(),
                        updatedAt: new Date()
                      }
                    }
                  }).filter(Boolean)

                  allHabits.push(...habits)

                  // 为每个习惯加载记录
                  for (const habit of habits) {
                    try {
                      const recordsResult = await databaseApi.getHabitRecords(habit.id)
                      if (recordsResult.success && recordsResult.data) {
                        const records: HabitRecord[] = recordsResult.data.map((dbRecord: any) => ({
                          id: dbRecord.id,
                          habitId: dbRecord.habitId,
                          date: dbRecord.date,
                          completed: dbRecord.completed || false,
                          value: dbRecord.value || 0
                        }))
                        allRecords.push(...records)
                      }
                    } catch (error) {
                      console.error(`Failed to load records for habit ${habit.id}:`, error)
                    }
                  }
                }
              } catch (error) {
                console.error(`Failed to load habits for area ${area.id}:`, error)
              }
            }

            // 更新状态
            console.log(`Total habits loaded: ${allHabits.length}`)
            console.log(`Total habit records loaded: ${allRecords.length}`)
            set({
              habits: allHabits,
              habitRecords: cleanHabitRecords(allRecords),
              loading: false
            })
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : 'Failed to fetch habits and records',
              loading: false
            })
          }
        }
      }),
      {
        name: 'area-store',
        partialize: (state) => ({
          areas: state.areas,
          currentArea: state.currentArea,
          habits: state.habits,
          habitRecords: state.habitRecords,
          metrics: state.metrics
        }),
        onRehydrateStorage: () => (state) => {
          if (state?.habitRecords) {
            // 清理恢复的数据中的无效日期
            state.habitRecords = cleanHabitRecords(state.habitRecords)
          }
        }
      }
    ),
    {
      name: 'area-store'
    }
  )
)
