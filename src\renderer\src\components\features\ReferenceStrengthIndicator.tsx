import React from 'react'

interface ReferenceStrengthIndicatorProps {
  strength: number
  size?: 'sm' | 'md' | 'lg'
  showLabel?: boolean
  showPercentage?: boolean
  className?: string
}

/**
 * 引用强度指示器组件
 * 可视化显示引用的强度
 */
export const ReferenceStrengthIndicator: React.FC<ReferenceStrengthIndicatorProps> = ({
  strength,
  size = 'md',
  showLabel = false,
  showPercentage = false,
  className = ''
}) => {
  // 确保强度在 0-1 范围内
  const normalizedStrength = Math.max(0, Math.min(1, strength))
  const percentage = Math.round(normalizedStrength * 100)

  // 根据强度获取颜色
  const getStrengthColor = () => {
    if (normalizedStrength >= 0.8) {
      return {
        bg: 'bg-green-500',
        text: 'text-green-600',
        light: 'bg-green-100'
      }
    } else if (normalizedStrength >= 0.6) {
      return {
        bg: 'bg-blue-500',
        text: 'text-blue-600',
        light: 'bg-blue-100'
      }
    } else if (normalizedStrength >= 0.4) {
      return {
        bg: 'bg-yellow-500',
        text: 'text-yellow-600',
        light: 'bg-yellow-100'
      }
    } else if (normalizedStrength >= 0.2) {
      return {
        bg: 'bg-orange-500',
        text: 'text-orange-600',
        light: 'bg-orange-100'
      }
    } else {
      return {
        bg: 'bg-red-500',
        text: 'text-red-600',
        light: 'bg-red-100'
      }
    }
  }

  // 根据尺寸获取样式
  const getSizeStyles = () => {
    switch (size) {
      case 'sm':
        return {
          container: 'w-12 h-2',
          dot: 'w-1 h-1',
          text: 'text-xs'
        }
      case 'lg':
        return {
          container: 'w-20 h-4',
          dot: 'w-2 h-2',
          text: 'text-base'
        }
      default: // md
        return {
          container: 'w-16 h-3',
          dot: 'w-1.5 h-1.5',
          text: 'text-sm'
        }
    }
  }

  // 获取强度描述
  const getStrengthLabel = () => {
    if (normalizedStrength >= 0.8) return '很强'
    if (normalizedStrength >= 0.6) return '较强'
    if (normalizedStrength >= 0.4) return '中等'
    if (normalizedStrength >= 0.2) return '较弱'
    return '很弱'
  }

  const colors = getStrengthColor()
  const sizeStyles = getSizeStyles()

  return (
    <div className={`reference-strength-indicator flex items-center space-x-2 ${className}`}>
      {/* 进度条样式 */}
      <div className={`relative ${sizeStyles.container} bg-gray-200 rounded-full overflow-hidden`}>
        <div 
          className={`absolute left-0 top-0 h-full ${colors.bg} transition-all duration-300 ease-out`}
          style={{ width: `${percentage}%` }}
        />
      </div>

      {/* 点状指示器（可选） */}
      <div className="flex items-center space-x-1">
        {[1, 2, 3, 4, 5].map((level) => (
          <div
            key={level}
            className={`${sizeStyles.dot} rounded-full transition-colors duration-200 ${
              normalizedStrength >= level * 0.2 
                ? colors.bg 
                : 'bg-gray-300'
            }`}
          />
        ))}
      </div>

      {/* 文字信息 */}
      <div className="flex items-center space-x-1">
        {showPercentage && (
          <span className={`${sizeStyles.text} font-medium ${colors.text}`}>
            {percentage}%
          </span>
        )}
        
        {showLabel && (
          <span className={`${sizeStyles.text} ${colors.text}`}>
            {getStrengthLabel()}
          </span>
        )}
      </div>
    </div>
  )
}

/**
 * 简化的强度徽章组件
 */
export const ReferenceStrengthBadge: React.FC<{
  strength: number
  className?: string
}> = ({ strength, className = '' }) => {
  const normalizedStrength = Math.max(0, Math.min(1, strength))
  const percentage = Math.round(normalizedStrength * 100)

  const getStrengthStyle = () => {
    if (normalizedStrength >= 0.8) {
      return 'bg-green-100 text-green-800 border-green-200'
    } else if (normalizedStrength >= 0.6) {
      return 'bg-blue-100 text-blue-800 border-blue-200'
    } else if (normalizedStrength >= 0.4) {
      return 'bg-yellow-100 text-yellow-800 border-yellow-200'
    } else if (normalizedStrength >= 0.2) {
      return 'bg-orange-100 text-orange-800 border-orange-200'
    } else {
      return 'bg-red-100 text-red-800 border-red-200'
    }
  }

  return (
    <span className={`inline-flex items-center px-2 py-1 text-xs font-medium border rounded-full ${getStrengthStyle()} ${className}`}>
      {percentage}%
    </span>
  )
}

/**
 * 强度图表组件（用于统计显示）
 */
export const ReferenceStrengthChart: React.FC<{
  strengths: number[]
  className?: string
}> = ({ strengths, className = '' }) => {
  // 计算强度分布
  const distribution = {
    veryStrong: strengths.filter(s => s >= 0.8).length,
    strong: strengths.filter(s => s >= 0.6 && s < 0.8).length,
    medium: strengths.filter(s => s >= 0.4 && s < 0.6).length,
    weak: strengths.filter(s => s >= 0.2 && s < 0.4).length,
    veryWeak: strengths.filter(s => s < 0.2).length
  }

  const total = strengths.length
  const average = total > 0 ? strengths.reduce((sum, s) => sum + s, 0) / total : 0

  return (
    <div className={`reference-strength-chart ${className}`}>
      <div className="mb-3">
        <div className="flex items-center justify-between text-sm text-gray-600 mb-1">
          <span>引用强度分布</span>
          <span>平均: {(average * 100).toFixed(0)}%</span>
        </div>
        
        {/* 分布条 */}
        <div className="flex h-2 bg-gray-200 rounded-full overflow-hidden">
          {total > 0 && (
            <>
              <div 
                className="bg-green-500" 
                style={{ width: `${(distribution.veryStrong / total) * 100}%` }}
                title={`很强: ${distribution.veryStrong}`}
              />
              <div 
                className="bg-blue-500" 
                style={{ width: `${(distribution.strong / total) * 100}%` }}
                title={`较强: ${distribution.strong}`}
              />
              <div 
                className="bg-yellow-500" 
                style={{ width: `${(distribution.medium / total) * 100}%` }}
                title={`中等: ${distribution.medium}`}
              />
              <div 
                className="bg-orange-500" 
                style={{ width: `${(distribution.weak / total) * 100}%` }}
                title={`较弱: ${distribution.weak}`}
              />
              <div 
                className="bg-red-500" 
                style={{ width: `${(distribution.veryWeak / total) * 100}%` }}
                title={`很弱: ${distribution.veryWeak}`}
              />
            </>
          )}
        </div>
      </div>

      {/* 图例 */}
      <div className="grid grid-cols-5 gap-1 text-xs">
        <div className="flex items-center space-x-1">
          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
          <span className="text-gray-600">{distribution.veryStrong}</span>
        </div>
        <div className="flex items-center space-x-1">
          <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
          <span className="text-gray-600">{distribution.strong}</span>
        </div>
        <div className="flex items-center space-x-1">
          <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
          <span className="text-gray-600">{distribution.medium}</span>
        </div>
        <div className="flex items-center space-x-1">
          <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
          <span className="text-gray-600">{distribution.weak}</span>
        </div>
        <div className="flex items-center space-x-1">
          <div className="w-2 h-2 bg-red-500 rounded-full"></div>
          <span className="text-gray-600">{distribution.veryWeak}</span>
        </div>
      </div>
    </div>
  )
}

export default ReferenceStrengthIndicator
