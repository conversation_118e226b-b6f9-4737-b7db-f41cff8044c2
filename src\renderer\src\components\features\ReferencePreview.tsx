import React, { useState, useEffect } from 'react'
import { UnifiedReference } from '../../services/unifiedReferenceService'

interface ReferencePreviewProps {
  reference: UnifiedReference
  onClose: () => void
  onNavigate: (reference: UnifiedReference) => void
  className?: string
}

/**
 * 引用预览组件
 * 显示引用的详细信息和预览内容
 */
export const ReferencePreview: React.FC<ReferencePreviewProps> = ({
  reference,
  onClose,
  onNavigate,
  className = ''
}) => {
  const [previewContent, setPreviewContent] = useState<string>('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // 加载预览内容
  useEffect(() => {
    const loadPreview = async () => {
      if (reference.referenceType !== 'wikilink') {
        // 非 WikiLink 引用暂时不支持预览
        return
      }

      setLoading(true)
      setError(null)

      try {
        // 这里应该调用文件系统 API 读取文件内容
        // 暂时使用模拟数据
        const mockContent = `# ${reference.targetTitle}

这是 ${reference.targetTitle} 的预览内容。

## 概述

这个文档包含了关于 ${reference.targetTitle} 的详细信息。

## 相关链接

- 相关文档1
- 相关文档2
- 相关文档3

## 标签

#示例 #预览 #文档`

        // 模拟加载延迟
        await new Promise(resolve => setTimeout(resolve, 300))
        
        setPreviewContent(mockContent)
      } catch (err) {
        console.error('❌ 加载预览内容失败:', err)
        setError(err instanceof Error ? err.message : '加载失败')
      } finally {
        setLoading(false)
      }
    }

    loadPreview()
  }, [reference])

  // 获取引用类型的图标和颜色
  const getReferenceStyle = () => {
    switch (reference.referenceType) {
      case 'wikilink':
        return {
          icon: '📄',
          color: 'text-blue-600',
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200'
        }
      case 'description':
      case 'task':
        return {
          icon: reference.referenceType === 'task' ? '✅' : '📋',
          color: 'text-green-600',
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200'
        }
      case 'note':
        return {
          icon: '🏷️',
          color: 'text-purple-600',
          bgColor: 'bg-purple-50',
          borderColor: 'border-purple-200'
        }
      default:
        return {
          icon: '📎',
          color: 'text-gray-600',
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200'
        }
    }
  }

  const style = getReferenceStyle()

  return (
    <div className={`reference-preview ${className}`}>
      {/* 遮罩层 */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-50 z-40"
        onClick={onClose}
      />
      
      {/* 预览窗口 */}
      <div className="fixed inset-4 md:inset-8 lg:inset-16 bg-white rounded-lg shadow-2xl z-50 flex flex-col">
        {/* 头部 */}
        <div className={`flex items-center justify-between p-4 border-b ${style.borderColor} ${style.bgColor}`}>
          <div className="flex items-center space-x-3">
            <span className="text-2xl">{style.icon}</span>
            <div>
              <h2 className={`text-lg font-semibold ${style.color}`}>
                {reference.targetTitle}
              </h2>
              <div className="flex items-center space-x-2 text-sm text-gray-500">
                <span className="capitalize">{reference.referenceType}</span>
                <span>•</span>
                <span>强度: {(reference.strength * 100).toFixed(0)}%</span>
                {reference.sourceType !== 'document' && (
                  <>
                    <span>•</span>
                    <span>来源: {reference.sourceTitle}</span>
                  </>
                )}
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={() => onNavigate(reference)}
              className={`px-3 py-1 text-sm font-medium ${style.color} ${style.bgColor} border ${style.borderColor} rounded hover:opacity-80 transition-opacity`}
            >
              跳转
            </button>
            <button
              onClick={onClose}
              className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* 引用上下文 */}
        {reference.context && (
          <div className="p-4 border-b border-gray-200 bg-gray-50">
            <h3 className="text-sm font-medium text-gray-700 mb-2">引用上下文</h3>
            <div className="text-sm text-gray-600 bg-white p-3 rounded border">
              {reference.context}
            </div>
          </div>
        )}

        {/* 内容区域 */}
        <div className="flex-1 overflow-hidden">
          {loading ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <div className="animate-spin w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"></div>
                <div className="text-gray-500">加载预览内容...</div>
              </div>
            </div>
          ) : error ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center text-red-500">
                <div className="text-4xl mb-4">❌</div>
                <div className="font-medium mb-2">加载失败</div>
                <div className="text-sm">{error}</div>
              </div>
            </div>
          ) : reference.referenceType === 'wikilink' ? (
            <div className="h-full overflow-y-auto p-4">
              <div className="prose prose-sm max-w-none">
                <pre className="whitespace-pre-wrap font-sans text-sm leading-relaxed">
                  {previewContent}
                </pre>
              </div>
            </div>
          ) : (
            <div className="flex items-center justify-center h-full">
              <div className="text-center text-gray-500">
                <div className="text-4xl mb-4">{style.icon}</div>
                <div className="font-medium mb-2">{reference.targetTitle}</div>
                <div className="text-sm">
                  {reference.referenceType === 'task' ? '任务引用' : 
                   reference.referenceType === 'description' ? '项目引用' : '领域引用'}
                  暂不支持预览
                </div>
                <button
                  onClick={() => onNavigate(reference)}
                  className={`mt-4 px-4 py-2 ${style.color} ${style.bgColor} border ${style.borderColor} rounded hover:opacity-80 transition-opacity`}
                >
                  跳转查看
                </button>
              </div>
            </div>
          )}
        </div>

        {/* 底部操作栏 */}
        <div className="flex items-center justify-between p-4 border-t border-gray-200 bg-gray-50">
          <div className="flex items-center space-x-4 text-sm text-gray-500">
            <span>创建时间: {new Date(reference.createdAt).toLocaleString()}</span>
            {reference.updatedAt !== reference.createdAt && (
              <span>更新时间: {new Date(reference.updatedAt).toLocaleString()}</span>
            )}
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-600 bg-white border border-gray-300 rounded hover:bg-gray-50 transition-colors"
            >
              关闭
            </button>
            <button
              onClick={() => onNavigate(reference)}
              className={`px-4 py-2 text-white ${style.color.replace('text-', 'bg-')} rounded hover:opacity-90 transition-opacity`}
            >
              打开
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ReferencePreview
