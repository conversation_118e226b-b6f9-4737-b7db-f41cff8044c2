# 智能输入功能测试说明

## 测试场景

### 1. 单方括号自动补全
**输入**: `[`
**期望**: 自动补全为 `[]`，光标位于中间
**测试方法**: 在编辑器中输入单个 `[`，观察是否自动补全

### 2. 双方括号自动补全
**输入**: `[[`
**期望**: 自动补全为 `[[]]`，光标位于中间
**测试方法**: 在编辑器中输入 `[[`，观察是否自动补全

### 3. WikiLink 完成
**输入**: `[[文档名]]`
**期望**: 转换为 WikiLink 节点，可点击跳转
**测试方法**: 完整输入 `[[测试文档]]`，观察是否转换为链接

### 4. 转义字符清理
**输入**: 包含 `\[` 或 `\]` 的内容
**期望**: 自动清理为 `[` 和 `]`
**测试方法**: 检查保存的内容是否包含转义字符

### 5. 智能退格
**输入**: 在 `[]` 或 `[[]]` 中间按退格
**期望**: 删除整个括号结构
**测试方法**: 在空括号中间按退格键

## 预期改进

1. **消除转义字符问题**: 输入 `[` 不再变成 `\[`
2. **提升输入体验**: 自动补全减少手动输入
3. **保持兼容性**: 现有 WikiLink 功能正常工作
4. **性能优化**: 减少不必要的解析和转换

## 注意事项

- 智能输入规则只在行尾触发，避免干扰正常编辑
- 双方括号规则优先级高于单方括号规则
- 转义字符清理在内容保存时自动执行
- 所有变更都有详细的控制台日志输出
