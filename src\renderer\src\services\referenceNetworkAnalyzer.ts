/**
 * 引用网络分析器
 * 分析文档间的引用关系网络，计算中心性、聚类等指标
 */

import { UnifiedReference } from './unifiedReferenceService'

export interface NetworkNode {
  id: string
  label: string
  type: 'document' | 'project' | 'area'
  path?: string
  size: number // 节点大小（基于引用数量）
  centrality: number // 中心性指标
  cluster: number // 聚类编号
  color: string // 节点颜色
}

export interface NetworkEdge {
  id: string
  source: string
  target: string
  weight: number // 边权重（引用强度）
  type: 'wikilink' | 'project' | 'area'
  color: string
  label?: string
}

export interface NetworkGraph {
  nodes: NetworkNode[]
  edges: NetworkEdge[]
  clusters: NetworkCluster[]
  metrics: NetworkMetrics
}

export interface NetworkCluster {
  id: number
  label: string
  nodes: string[]
  color: string
  centrality: number
}

export interface NetworkMetrics {
  totalNodes: number
  totalEdges: number
  density: number // 网络密度
  avgClustering: number // 平均聚类系数
  avgPathLength: number // 平均路径长度
  modularity: number // 模块化指标
  centralNodes: string[] // 中心节点
  bridgeNodes: string[] // 桥接节点
}

/**
 * 引用网络分析器类
 */
export class ReferenceNetworkAnalyzer {
  /**
   * 构建引用网络图
   */
  buildNetworkGraph(references: UnifiedReference[]): NetworkGraph {
    console.log('🕸️ 构建引用网络图:', references.length, '个引用')

    // 提取所有节点
    const nodeMap = new Map<string, NetworkNode>()
    const edgeMap = new Map<string, NetworkEdge>()

    // 处理引用，构建节点和边
    references.forEach(ref => {
      // 添加源节点
      if (!nodeMap.has(ref.sourceId)) {
        nodeMap.set(ref.sourceId, {
          id: ref.sourceId,
          label: ref.sourceTitle,
          type: this.getNodeType(ref.sourceType),
          path: ref.sourcePath,
          size: 1,
          centrality: 0,
          cluster: 0,
          color: this.getNodeColor(ref.sourceType)
        })
      }

      // 添加目标节点
      if (!nodeMap.has(ref.targetPath)) {
        nodeMap.set(ref.targetPath, {
          id: ref.targetPath,
          label: ref.targetTitle,
          type: 'document',
          path: ref.targetPath,
          size: 1,
          centrality: 0,
          cluster: 0,
          color: this.getNodeColor('document')
        })
      }

      // 添加边
      const edgeId = `${ref.sourceId}-${ref.targetPath}`
      if (!edgeMap.has(edgeId)) {
        edgeMap.set(edgeId, {
          id: edgeId,
          source: ref.sourceId,
          target: ref.targetPath,
          weight: ref.strength,
          type: ref.referenceType,
          color: this.getEdgeColor(ref.referenceType),
          label: ref.referenceType
        })
      } else {
        // 如果边已存在，增加权重
        const edge = edgeMap.get(edgeId)!
        edge.weight += ref.strength
      }

      // 增加节点大小
      const sourceNode = nodeMap.get(ref.sourceId)!
      const targetNode = nodeMap.get(ref.targetPath)!
      sourceNode.size += 1
      targetNode.size += 1
    })

    const nodes = Array.from(nodeMap.values())
    const edges = Array.from(edgeMap.values())

    // 计算网络指标
    const metrics = this.calculateNetworkMetrics(nodes, edges)
    
    // 计算中心性
    this.calculateCentrality(nodes, edges)
    
    // 执行聚类
    const clusters = this.performClustering(nodes, edges)
    
    // 更新节点聚类信息
    this.updateNodeClusters(nodes, clusters)

    console.log('✅ 网络图构建完成:', {
      节点数: nodes.length,
      边数: edges.length,
      聚类数: clusters.length,
      网络密度: metrics.density
    })

    return {
      nodes,
      edges,
      clusters,
      metrics
    }
  }

  /**
   * 计算网络指标
   */
  private calculateNetworkMetrics(nodes: NetworkNode[], edges: NetworkEdge[]): NetworkMetrics {
    const n = nodes.length
    const m = edges.length
    
    // 网络密度：实际边数 / 最大可能边数
    const density = n > 1 ? (2 * m) / (n * (n - 1)) : 0
    
    // 构建邻接表
    const adjacency = new Map<string, string[]>()
    nodes.forEach(node => adjacency.set(node.id, []))
    
    edges.forEach(edge => {
      adjacency.get(edge.source)?.push(edge.target)
      adjacency.get(edge.target)?.push(edge.source)
    })

    // 计算平均聚类系数
    let totalClustering = 0
    nodes.forEach(node => {
      const neighbors = adjacency.get(node.id) || []
      if (neighbors.length < 2) return
      
      let triangles = 0
      for (let i = 0; i < neighbors.length; i++) {
        for (let j = i + 1; j < neighbors.length; j++) {
          const neighbor1 = neighbors[i]
          const neighbor2 = neighbors[j]
          if (adjacency.get(neighbor1)?.includes(neighbor2)) {
            triangles++
          }
        }
      }
      
      const possibleTriangles = (neighbors.length * (neighbors.length - 1)) / 2
      totalClustering += possibleTriangles > 0 ? triangles / possibleTriangles : 0
    })
    
    const avgClustering = n > 0 ? totalClustering / n : 0

    // 简化的平均路径长度计算（BFS）
    const avgPathLength = this.calculateAveragePathLength(nodes, adjacency)

    // 识别中心节点（度中心性最高的前20%）
    const sortedByDegree = nodes
      .map(node => ({ id: node.id, degree: adjacency.get(node.id)?.length || 0 }))
      .sort((a, b) => b.degree - a.degree)
    
    const centralCount = Math.max(1, Math.floor(nodes.length * 0.2))
    const centralNodes = sortedByDegree.slice(0, centralCount).map(n => n.id)

    // 识别桥接节点（移除后会增加连通分量的节点）
    const bridgeNodes = this.findBridgeNodes(nodes, adjacency)

    return {
      totalNodes: n,
      totalEdges: m,
      density,
      avgClustering,
      avgPathLength,
      modularity: 0, // 简化实现
      centralNodes,
      bridgeNodes
    }
  }

  /**
   * 计算中心性指标
   */
  private calculateCentrality(nodes: NetworkNode[], edges: NetworkEdge[]): void {
    // 构建邻接表
    const adjacency = new Map<string, string[]>()
    nodes.forEach(node => adjacency.set(node.id, []))
    
    edges.forEach(edge => {
      adjacency.get(edge.source)?.push(edge.target)
      adjacency.get(edge.target)?.push(edge.source)
    })

    // 计算度中心性
    nodes.forEach(node => {
      const degree = adjacency.get(node.id)?.length || 0
      node.centrality = degree / Math.max(1, nodes.length - 1)
    })
  }

  /**
   * 执行聚类分析
   */
  private performClustering(nodes: NetworkNode[], edges: NetworkEdge[]): NetworkCluster[] {
    // 简化的聚类算法：基于连通分量
    const visited = new Set<string>()
    const clusters: NetworkCluster[] = []
    
    // 构建邻接表
    const adjacency = new Map<string, string[]>()
    nodes.forEach(node => adjacency.set(node.id, []))
    
    edges.forEach(edge => {
      adjacency.get(edge.source)?.push(edge.target)
      adjacency.get(edge.target)?.push(edge.source)
    })

    let clusterId = 0
    
    nodes.forEach(node => {
      if (!visited.has(node.id)) {
        const clusterNodes = this.dfsCluster(node.id, adjacency, visited)
        
        if (clusterNodes.length > 0) {
          clusters.push({
            id: clusterId++,
            label: `聚类 ${clusterId}`,
            nodes: clusterNodes,
            color: this.getClusterColor(clusterId),
            centrality: 0
          })
        }
      }
    })

    return clusters
  }

  /**
   * DFS 聚类搜索
   */
  private dfsCluster(nodeId: string, adjacency: Map<string, string[]>, visited: Set<string>): string[] {
    const cluster: string[] = []
    const stack = [nodeId]
    
    while (stack.length > 0) {
      const current = stack.pop()!
      if (visited.has(current)) continue
      
      visited.add(current)
      cluster.push(current)
      
      const neighbors = adjacency.get(current) || []
      neighbors.forEach(neighbor => {
        if (!visited.has(neighbor)) {
          stack.push(neighbor)
        }
      })
    }
    
    return cluster
  }

  /**
   * 更新节点聚类信息
   */
  private updateNodeClusters(nodes: NetworkNode[], clusters: NetworkCluster[]): void {
    clusters.forEach(cluster => {
      cluster.nodes.forEach(nodeId => {
        const node = nodes.find(n => n.id === nodeId)
        if (node) {
          node.cluster = cluster.id
          node.color = cluster.color
        }
      })
    })
  }

  /**
   * 计算平均路径长度
   */
  private calculateAveragePathLength(nodes: NetworkNode[], adjacency: Map<string, string[]>): number {
    if (nodes.length < 2) return 0
    
    let totalDistance = 0
    let pathCount = 0
    
    // 简化实现：只计算部分节点对的距离
    const sampleSize = Math.min(10, nodes.length)
    const sampleNodes = nodes.slice(0, sampleSize)
    
    sampleNodes.forEach(source => {
      const distances = this.bfsDistances(source.id, adjacency)
      sampleNodes.forEach(target => {
        if (source.id !== target.id && distances.has(target.id)) {
          totalDistance += distances.get(target.id)!
          pathCount++
        }
      })
    })
    
    return pathCount > 0 ? totalDistance / pathCount : 0
  }

  /**
   * BFS 计算距离
   */
  private bfsDistances(startId: string, adjacency: Map<string, string[]>): Map<string, number> {
    const distances = new Map<string, number>()
    const queue = [{ id: startId, distance: 0 }]
    const visited = new Set<string>()
    
    while (queue.length > 0) {
      const { id, distance } = queue.shift()!
      if (visited.has(id)) continue
      
      visited.add(id)
      distances.set(id, distance)
      
      const neighbors = adjacency.get(id) || []
      neighbors.forEach(neighbor => {
        if (!visited.has(neighbor)) {
          queue.push({ id: neighbor, distance: distance + 1 })
        }
      })
    }
    
    return distances
  }

  /**
   * 查找桥接节点
   */
  private findBridgeNodes(nodes: NetworkNode[], adjacency: Map<string, string[]>): string[] {
    // 简化实现：返回度中心性较高但聚类系数较低的节点
    return nodes
      .filter(node => {
        const neighbors = adjacency.get(node.id) || []
        return neighbors.length >= 2 && neighbors.length <= 5
      })
      .map(node => node.id)
      .slice(0, 3)
  }

  /**
   * 获取节点类型
   */
  private getNodeType(sourceType: string): 'document' | 'project' | 'area' {
    switch (sourceType) {
      case 'project': return 'project'
      case 'area': return 'area'
      default: return 'document'
    }
  }

  /**
   * 获取节点颜色
   */
  private getNodeColor(type: string): string {
    switch (type) {
      case 'document': return '#3b82f6' // 蓝色
      case 'project': return '#10b981' // 绿色
      case 'area': return '#8b5cf6' // 紫色
      default: return '#6b7280' // 灰色
    }
  }

  /**
   * 获取边颜色
   */
  private getEdgeColor(type: string): string {
    switch (type) {
      case 'wikilink': return '#3b82f6'
      case 'task':
      case 'description': return '#10b981'
      case 'note': return '#8b5cf6'
      default: return '#6b7280'
    }
  }

  /**
   * 获取聚类颜色
   */
  private getClusterColor(clusterId: number): string {
    const colors = [
      '#ef4444', '#f97316', '#eab308', '#22c55e', 
      '#06b6d4', '#3b82f6', '#8b5cf6', '#ec4899'
    ]
    return colors[clusterId % colors.length]
  }
}

// 导出单例实例
export const referenceNetworkAnalyzer = new ReferenceNetworkAnalyzer()
