import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '../ui/button'
import { Input } from '../ui/input'
import { Textarea } from '../ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { Separator } from '../ui/separator'
import { Calendar, Clock, Save, X, Plus, Trash2, <PERSON><PERSON><PERSON>, RefreshCw, Brain } from 'lucide-react'
import { useLanguage } from '../../contexts/LanguageContext'
import { databaseApi } from '../../lib/api'
import type { Review, ReviewTemplate } from '../../../../shared/types'
import type { CreateReviewRequest, UpdateReviewRequest } from '../../../../shared/ipcTypes'
import { reviewTemplatesI18n } from '../../../../shared/i18n/reviews'
import ReviewAnalysis from './ReviewAnalysis'

interface ReviewEditorProps {
  review?: Review
  onSave?: (review: Review) => void
  onCancel?: () => void
  onClose?: () => void
}

interface ActionItem {
  id: string
  text: string
  completed: boolean
  priority: 'low' | 'medium' | 'high'
}

export function ReviewEditor({ review, onSave, onCancel, onClose }: ReviewEditorProps) {
  const { t, language } = useLanguage()
  const [loading, setLoading] = useState(false)
  const [generatingContent, setGeneratingContent] = useState(false)
  const [showAnalysis, setShowAnalysis] = useState(false)
  const [templates, setTemplates] = useState<ReviewTemplate[]>([])
  const [selectedTemplate, setSelectedTemplate] = useState<ReviewTemplate | null>(null)

  // 获取动态国际化文本的函数
  const getI18nSectionText = (sectionId: string, reviewType: string, field: 'title' | 'description' | 'placeholder') => {
    const i18nData = reviewTemplatesI18n[language]
    const typeData = i18nData[reviewType as keyof typeof i18nData]

    if (typeData && typeData.sections && typeData.sections[sectionId as keyof typeof typeData.sections]) {
      const sectionData = typeData.sections[sectionId as keyof typeof typeData.sections]
      return sectionData[field] || ''
    }

    // 如果找不到对应的国际化文本，返回原始文本作为后备
    return ''
  }

  // 获取模板的国际化名称
  const getI18nTemplateName = (templateType: string) => {
    const i18nData = reviewTemplatesI18n[language]
    const typeData = i18nData[templateType as keyof typeof i18nData]
    return typeData?.name || ''
  }

  // Form state
  const [formData, setFormData] = useState({
    type: review?.type || 'weekly',
    period: review?.period || '',
    title: review?.title || '',
    summary: review?.summary || '',
    templateId: review?.templateId || '',
    status: review?.status || 'draft'
  })

  // Dynamic content sections based on template
  const [content, setContent] = useState<Record<string, string>>(
    review?.content || {}
  )
  
  // Action items
  const [actionItems, setActionItems] = useState<ActionItem[]>(
    review?.actionItems || []
  )
  
  // Insights
  const [insights, setInsights] = useState({
    mood: '',
    energy: '',
    productivity: '',
    satisfaction: '',
    ...review?.insights
  })

  useEffect(() => {
    loadTemplates()
    if (!review) {
      generatePeriod(formData.type)
    }
  }, [])

  useEffect(() => {
    loadTemplates()
  }, [formData.type])

  useEffect(() => {
    // Load selected template when templateId changes
    if (formData.templateId && templates.length > 0) {
      const template = templates.find(t => t.id === formData.templateId)
      if (template) {
        setSelectedTemplate(template)
        // Initialize content sections based on template
        const initialContent: Record<string, string> = {}
        template.structure?.sections?.forEach((section) => {
          if (!content[section.id]) {
            initialContent[section.id] = ''
          }
        })
        if (Object.keys(initialContent).length > 0) {
          setContent((prev) => ({ ...prev, ...initialContent }))
        }
      }
    }
  }, [formData.templateId, templates])

  const loadTemplates = async () => {
    try {
      const result = await databaseApi.getReviewTemplates(formData.type)
      if (result.success) {
        setTemplates(result.data || [])
        // Auto-select default template
        const defaultTemplate = result.data?.find((t) => t.isDefault && t.type === formData.type)
        if (defaultTemplate && !formData.templateId) {
          setFormData((prev) => ({ ...prev, templateId: defaultTemplate.id }))
        }
      }
    } catch (error) {
      console.error('Failed to load templates:', error)
    }
  }

  const generatePeriod = (type: string) => {
    const now = new Date()
    let period = ''
    
    switch (type) {
      case 'daily':
        period = now.toISOString().split('T')[0] // YYYY-MM-DD
        break
      case 'weekly':
        const year = now.getFullYear()
        const week = getWeekNumber(now)
        period = `${year}-W${week.toString().padStart(2, '0')}`
        break
      case 'monthly':
        period = `${now.getFullYear()}-${(now.getMonth() + 1).toString().padStart(2, '0')}`
        break
      case 'quarterly':
        const quarter = Math.ceil((now.getMonth() + 1) / 3)
        period = `${now.getFullYear()}-Q${quarter}`
        break
      case 'yearly':
        period = now.getFullYear().toString()
        break
    }
    
    setFormData(prev => ({ ...prev, period }))
  }

  const getWeekNumber = (date: Date) => {
    const d = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()))
    const dayNum = d.getUTCDay() || 7
    d.setUTCDate(d.getUTCDate() + 4 - dayNum)
    const yearStart = new Date(Date.UTC(d.getUTCFullYear(), 0, 1))
    return Math.ceil(((d.getTime() - yearStart.getTime()) / 86400000 + 1) / 7)
  }

  const handleTypeChange = (type: string) => {
    setFormData(prev => ({ ...prev, type }))
    generatePeriod(type)
    loadTemplates()
  }

  const addActionItem = () => {
    const newItem: ActionItem = {
      id: Date.now().toString(),
      text: '',
      completed: false,
      priority: 'medium'
    }
    setActionItems(prev => [...prev, newItem])
  }

  const updateActionItem = (id: string, updates: Partial<ActionItem>) => {
    setActionItems((prev) =>
      prev.map((item) => (item.id === id ? { ...item, ...updates } : item))
    )
  }

  const removeActionItem = (id: string) => {
    setActionItems(prev => prev.filter(item => item.id !== id))
  }

  const generateContent = async () => {
    if (!formData.type || !formData.period) {
      return
    }

    setGeneratingContent(true)
    try {
      const result = await databaseApi.getReviewAggregatedData(formData.type, formData.period)
      if (result.success && result.data) {
        const data = result.data

        // Generate content based on aggregated data and template structure
        const generatedContent: Record<string, string> = {}

        // Prepare content snippets
        const achievements = []
        const challenges = []
        const learnings = []
        const nextSteps = []

        // Projects achievements
        if (data.projects?.completed > 0) {
          achievements.push(`✅ Completed ${data.projects.completed} project${data.projects.completed > 1 ? 's' : ''}`)
        }
        if (data.projects?.completionRate > 75) {
          achievements.push(`🎯 Achieved ${data.projects.completionRate}% project completion rate`)
        }

        // Task achievements
        if (data.tasks?.completed > 0) {
          achievements.push(`📋 Completed ${data.tasks.completed} tasks`)
        }

        // Habit achievements
        if (data.areas?.habitCompletionRate > 70) {
          achievements.push(`💪 Maintained ${data.areas.habitCompletionRate}% habit consistency`)
        }

        // Challenges
        if (data.tasks?.overdue > 0) {
          challenges.push(`⏰ ${data.tasks.overdue} tasks became overdue`)
        }
        if (data.projects?.completionRate < 50) {
          challenges.push(`📉 Project completion rate was only ${data.projects.completionRate}%`)
        }
        if (data.areas?.habitCompletionRate < 50) {
          challenges.push(`🔄 Habit consistency dropped to ${data.areas.habitCompletionRate}%`)
        }

        // Learnings from insights
        if (data.insights?.achievements?.length > 0) {
          learnings.push(...data.insights.achievements.map(a => `💡 ${a}`))
        }

        // Next steps from improvement areas
        if (data.insights?.improvementAreas?.length > 0) {
          nextSteps.push(...data.insights.improvementAreas.map(area => `🚀 ${area}`))
        }

        // Map content to template sections
        if (selectedTemplate?.structure?.sections) {
          selectedTemplate.structure.sections.forEach((section) => {
            const sectionId = section.id.toLowerCase()

            if (sectionId.includes('achievement') || sectionId.includes('win') || sectionId.includes('success')) {
              generatedContent[section.id] = achievements.join('\n')
            } else if (sectionId.includes('challenge') || sectionId.includes('obstacle') || sectionId.includes('difficult')) {
              generatedContent[section.id] = challenges.join('\n')
            } else if (sectionId.includes('learning') || sectionId.includes('insight') || sectionId.includes('lesson')) {
              generatedContent[section.id] = learnings.join('\n')
            } else if (sectionId.includes('next') || sectionId.includes('future') || sectionId.includes('goal') || sectionId.includes('plan')) {
              generatedContent[section.id] = nextSteps.join('\n')
            } else {
              // Default content for unmatched sections
              generatedContent[section.id] = `Generated content for ${section.title}:\n${achievements.slice(0, 2).join('\n')}`
            }
          })
        } else {
          // Fallback to default structure
          generatedContent.achievements = achievements.join('\n')
          generatedContent.challenges = challenges.join('\n')
          generatedContent.learnings = learnings.join('\n')
          generatedContent.nextSteps = nextSteps.join('\n')
        }

        // Update content
        setContent((prev) => ({
          ...prev,
          ...generatedContent
        }))

        // Generate summary
        const summaryParts = []
        if (data.projects?.completed > 0) {
          summaryParts.push(`${data.projects.completed} projects completed`)
        }
        if (data.tasks?.completed > 0) {
          summaryParts.push(`${data.tasks.completed} tasks finished`)
        }
        if (data.areas?.habitCompletionRate > 0) {
          summaryParts.push(`${data.areas.habitCompletionRate}% habit consistency`)
        }

        setFormData((prev) => ({
          ...prev,
          summary: summaryParts.join(', ')
        }))

        // Update insights
        setInsights(prev => ({
          ...prev,
          productivity: data.tasks?.completionRate > 75 ? 'high' : data.tasks?.completionRate > 50 ? 'medium' : 'low',
          satisfaction: data.projects?.completionRate > 80 ? 'very-satisfied' : data.projects?.completionRate > 60 ? 'satisfied' : 'neutral'
        }))

        console.log('Content generated successfully from aggregated data')
      } else {
        console.error('Failed to get aggregated data:', result.error)
      }
    } catch (error) {
      console.error('Error generating content:', error)
    } finally {
      setGeneratingContent(false)
    }
  }

  const handleSave = async () => {
    setLoading(true)
    try {
      const reviewData = {
        ...formData,
        content,
        insights,
        actionItems,
        title: formData.title || `${t(`pages.reviews.editor.types.${formData.type}`)} - ${formData.period}`
      }

      let result
      if (review?.id) {
        // Update existing review
        const updateData: UpdateReviewRequest = {
          id: review.id,
          updates: reviewData
        }
        result = await databaseApi.updateReview(updateData)
      } else {
        // Create new review
        const createData: CreateReviewRequest = reviewData
        result = await databaseApi.createReview(createData)
      }

      if (result.success) {
        onSave?.(result.data)
      } else {
        console.error('Failed to save review:', result.error)
      }
    } catch (error) {
      console.error('Error saving review:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Calendar className="h-6 w-6 text-primary" />
          <div>
            <h1 className="text-2xl font-bold">
              {review ? t('pages.reviews.editor.editReview') : t('pages.reviews.editor.newReview')}
            </h1>
            <p className="text-muted-foreground">
              {formData.period && `${t('pages.reviews.editor.period')}: ${formData.period}`}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant={formData.status === 'completed' ? 'default' : 'secondary'}>
            {t(`pages.reviews.editor.status.${formData.status}`)}
          </Badge>
          {formData.period && (
            <Button
              variant="outline"
              onClick={() => setShowAnalysis(true)}
              disabled={!formData.period}
            >
              <Brain className="h-4 w-4 mr-2" />
              {t('pages.reviews.editor.aiAnalysis')}
            </Button>
          )}
          <Button variant="outline" onClick={onCancel || onClose}>
            <X className="h-4 w-4 mr-2" />
            {t('pages.reviews.editor.cancel')}
          </Button>
          <Button onClick={handleSave} disabled={loading}>
            <Save className="h-4 w-4 mr-2" />
            {loading ? t('pages.reviews.editor.saving') : t('pages.reviews.editor.save')}
          </Button>
        </div>
      </div>

      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle>{t('pages.reviews.editor.basicInfo')}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">{t('pages.reviews.editor.reviewType')}</label>
              <Select value={formData.type} onValueChange={handleTypeChange}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="daily">{t('pages.reviews.editor.types.daily')}</SelectItem>
                  <SelectItem value="weekly">{t('pages.reviews.editor.types.weekly')}</SelectItem>
                  <SelectItem value="monthly">{t('pages.reviews.editor.types.monthly')}</SelectItem>
                  <SelectItem value="quarterly">{t('pages.reviews.editor.types.quarterly')}</SelectItem>
                  <SelectItem value="yearly">{t('pages.reviews.editor.types.yearly')}</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium">{t('pages.reviews.editor.period')}</label>
              <Input
                value={formData.period}
                onChange={(e) => setFormData((prev) => ({ ...prev, period: e.target.value }))}
                placeholder={t('pages.reviews.editor.periodPlaceholder')}
              />
            </div>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">{t('pages.reviews.editor.template')}</label>
            <Select
              value={formData.templateId}
              onValueChange={(value) => setFormData(prev => ({ ...prev, templateId: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder={t('pages.reviews.editor.selectTemplate')} />
              </SelectTrigger>
              <SelectContent>
                {templates.map((template) => {
                  const i18nName = getI18nTemplateName(template.type)
                  const displayName = i18nName || template.name
                  return (
                    <SelectItem key={template.id} value={template.id}>
                      {displayName} {template.isDefault && `(${t('pages.reviews.templates.defaultTemplate')})`}
                    </SelectItem>
                  )
                })}
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-2">
            <label className="text-sm font-medium">{t('pages.reviews.editor.reviewTitle')}</label>
            <Input
              value={formData.title}
              onChange={(e) => setFormData((prev) => ({ ...prev, title: e.target.value }))}
              placeholder={t('pages.reviews.editor.titlePlaceholder')}
            />
          </div>
          
          <div className="space-y-2">
            <label className="text-sm font-medium">{t('pages.reviews.editor.summary')}</label>
            <Textarea
              value={formData.summary}
              onChange={(e) => setFormData((prev) => ({ ...prev, summary: e.target.value }))}
              placeholder={t('pages.reviews.editor.summaryPlaceholder')}
              rows={3}
            />
          </div>
        </CardContent>
      </Card>

      {/* Content Sections */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
{t('pages.reviews.editor.content')}
            <Button
              variant="outline"
              size="sm"
              onClick={generateContent}
              disabled={generatingContent || !formData.period}
            >
              {generatingContent ? (
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Sparkles className="h-4 w-4 mr-2" />
              )}
              {generatingContent ? t('pages.reviews.editor.generating') : t('pages.reviews.editor.autoGenerate')}
            </Button>
          </CardTitle>
          <CardDescription>
            {t('pages.reviews.editor.contentDescription')}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {selectedTemplate?.structure?.sections ? (
            // Render dynamic sections based on template
            selectedTemplate.structure.sections.map((section) => {
              // 获取动态国际化文本
              const i18nTitle = getI18nSectionText(section.id, formData.type, 'title')
              const i18nDescription = getI18nSectionText(section.id, formData.type, 'description')
              const i18nPlaceholder = getI18nSectionText(section.id, formData.type, 'placeholder')

              return (
                <div key={section.id} className="space-y-2">
                  <label className="text-sm font-medium">
                    {i18nTitle || section.title}
                    {section.required && <span className="text-red-500 ml-1">*</span>}
                  </label>
                  {(i18nDescription || section.description) && (
                    <p className="text-xs text-muted-foreground">
                      {i18nDescription || section.description}
                    </p>
                  )}
                  <Textarea
                    value={content[section.id] || ''}
                    onChange={(e) => setContent(prev => ({ ...prev, [section.id]: e.target.value }))}
                    placeholder={i18nPlaceholder || section.placeholder}
                    rows={4}
                  />
                </div>
              )
            })
          ) : (
            // Fallback to default sections
            <>
              <div className="space-y-2">
                <label className="text-sm font-medium">🎉 {t('pages.reviews.editor.defaultSections.achievements')}</label>
                <Textarea
                  value={content.achievements || ''}
                  onChange={(e) => setContent(prev => ({ ...prev, achievements: e.target.value }))}
                  placeholder={t('pages.reviews.editor.fallbackPlaceholders.achievements')}
                  rows={4}
                />
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">⚡ {t('pages.reviews.editor.defaultSections.challenges')}</label>
                <Textarea
                  value={content.challenges || ''}
                  onChange={(e) => setContent(prev => ({ ...prev, challenges: e.target.value }))}
                  placeholder={t('pages.reviews.editor.fallbackPlaceholders.challenges')}
                  rows={4}
                />
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">💡 {t('pages.reviews.editor.defaultSections.learnings')}</label>
                <Textarea
                  value={content.learnings || ''}
                  onChange={(e) => setContent(prev => ({ ...prev, learnings: e.target.value }))}
                  placeholder={t('pages.reviews.editor.fallbackPlaceholders.learnings')}
                  rows={4}
                />
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">🚀 {t('pages.reviews.editor.defaultSections.nextSteps')}</label>
                <Textarea
                  value={content.nextSteps || ''}
                  onChange={(e) => setContent((prev) => ({ ...prev, nextSteps: e.target.value }))}
                  placeholder={t('pages.reviews.editor.fallbackPlaceholders.nextSteps')}
                  rows={4}
                />
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Action Items */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
{t('pages.reviews.editor.actionItems')}
            <Button variant="outline" size="sm" onClick={addActionItem}>
              <Plus className="h-4 w-4 mr-2" />
{t('pages.reviews.editor.addActionItem')}
            </Button>
          </CardTitle>
          <CardDescription>{t('pages.reviews.editor.actionItemsDescription')}</CardDescription>
        </CardHeader>
        <CardContent className="space-y-3">
          {actionItems.length === 0 ? (
            <p className="text-muted-foreground text-center py-4">
              {t('pages.reviews.editor.noActionItemsTip')}
            </p>
          ) : (
            actionItems.map((item) => (
              <div key={item.id} className="flex items-center gap-3 p-3 border rounded-lg">
                <input
                  type="checkbox"
                  checked={item.completed}
                  onChange={(e) => updateActionItem(item.id, { completed: e.target.checked })}
                  className="h-4 w-4"
                />
                <Input
                  value={item.text}
                  onChange={(e) => updateActionItem(item.id, { text: e.target.value })}
                  placeholder={t('pages.reviews.editor.actionItemPlaceholder')}
                  className="flex-1"
                />
                <Select
                  value={item.priority}
                  onValueChange={(value: 'low' | 'medium' | 'high') =>
                    updateActionItem(item.id, { priority: value })
                  }
                >
                  <SelectTrigger className="w-24">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">{t('pages.reviews.editor.priorities.low')}</SelectItem>
                    <SelectItem value="medium">{t('pages.reviews.editor.priorities.medium')}</SelectItem>
                    <SelectItem value="high">{t('pages.reviews.editor.priorities.high')}</SelectItem>
                  </SelectContent>
                </Select>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removeActionItem(item.id)}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            ))
          )}
        </CardContent>
      </Card>

      {/* Insights & Metrics */}
      <Card>
        <CardHeader>
          <CardTitle>{t('pages.reviews.editor.insights')}</CardTitle>
          <CardDescription>
            {t('pages.reviews.editor.insightsDescription')}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">😊 {t('pages.reviews.editor.mood')}</label>
              <Select
                value={insights.mood}
                onValueChange={(value) => setInsights((prev) => ({ ...prev, mood: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder={t('pages.reviews.editor.placeholders.selectMood')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="excellent">😄 {t('pages.reviews.editor.moodLevels.excellent')}</SelectItem>
                  <SelectItem value="good">😊 {t('pages.reviews.editor.moodLevels.good')}</SelectItem>
                  <SelectItem value="neutral">😐 {t('pages.reviews.editor.moodLevels.neutral')}</SelectItem>
                  <SelectItem value="poor">😔 {t('pages.reviews.editor.moodLevels.poor')}</SelectItem>
                  <SelectItem value="terrible">😞 {t('pages.reviews.editor.moodLevels.terrible')}</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">⚡ {t('pages.reviews.editor.energy')}</label>
              <Select
                value={insights.energy}
                onValueChange={(value) => setInsights((prev) => ({ ...prev, energy: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder={t('pages.reviews.editor.placeholders.selectEnergy')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="high">🔥 High</SelectItem>
                  <SelectItem value="medium">⚡ Medium</SelectItem>
                  <SelectItem value="low">🔋 Low</SelectItem>
                  <SelectItem value="depleted">😴 Depleted</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">🎯 {t('pages.reviews.editor.productivity')}</label>
              <Select
                value={insights.productivity}
                onValueChange={(value) => setInsights((prev) => ({ ...prev, productivity: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder={t('pages.reviews.editor.placeholders.selectProductivity')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="very-high">🚀 {t('pages.reviews.editor.productivityLevels.veryHigh')}</SelectItem>
                  <SelectItem value="high">📈 {t('pages.reviews.editor.productivityLevels.high')}</SelectItem>
                  <SelectItem value="medium">📊 {t('pages.reviews.editor.productivityLevels.medium')}</SelectItem>
                  <SelectItem value="low">📉 {t('pages.reviews.editor.productivityLevels.low')}</SelectItem>
                  <SelectItem value="very-low">🐌 {t('pages.reviews.editor.productivityLevels.veryLow')}</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">✨ {t('pages.reviews.editor.satisfaction')}</label>
              <Select
                value={insights.satisfaction}
                onValueChange={(value) => setInsights((prev) => ({ ...prev, satisfaction: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder={t('pages.reviews.editor.placeholders.selectSatisfaction')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="very-satisfied">🌟 {t('pages.reviews.editor.satisfactionLevels.verySatisfied')}</SelectItem>
                  <SelectItem value="satisfied">😊 {t('pages.reviews.editor.satisfactionLevels.satisfied')}</SelectItem>
                  <SelectItem value="neutral">😐 {t('pages.reviews.editor.satisfactionLevels.neutral')}</SelectItem>
                  <SelectItem value="dissatisfied">😕 {t('pages.reviews.editor.satisfactionLevels.dissatisfied')}</SelectItem>
                  <SelectItem value="very-dissatisfied">😞 {t('pages.reviews.editor.satisfactionLevels.veryDissatisfied')}</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* AI Analysis Modal */}
      {showAnalysis && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-6xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <ReviewAnalysis
                type={formData.type}
                period={formData.period}
                onClose={() => setShowAnalysis(false)}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default ReviewEditor
