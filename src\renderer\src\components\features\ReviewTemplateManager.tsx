import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '../ui/button'
import { Input } from '../ui/input'
import { Textarea } from '../ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '../ui/dialog'
import { Plus, Edit, Trash2, Star, Copy, Download, Upload, Settings } from 'lucide-react'
import { useLanguage } from '../../contexts/LanguageContext'
import { databaseApi } from '../../lib/api'
import type { ReviewTemplate } from '../../../../shared/types'
import type { CreateReviewTemplateRequest, UpdateReviewTemplateRequest } from '../../../../shared/ipcTypes'
import { reviewTemplatesI18n } from '../../../../shared/i18n/reviews'

interface ReviewTemplateManagerProps {
  onTemplateSelect?: (template: ReviewTemplate) => void
}

interface TemplateSection {
  id: string
  title: string
  description: string
  placeholder: string
  required: boolean
}

interface TemplateFormData {
  name: string
  description: string
  type: string
  structure: {
    sections: TemplateSection[]
  }
  isDefault: boolean
}

// 这些默认章节将在组件内部使用翻译函数动态生成
const getDefaultSections = (t: (key: string) => string) => ({
  daily: [
    { id: 'wins', title: t('pages.reviews.templates.defaultSections.daily.wins.title'), description: t('pages.reviews.templates.defaultSections.daily.wins.description'), placeholder: t('pages.reviews.templates.defaultSections.daily.wins.placeholder'), required: true },
    { id: 'challenges', title: t('pages.reviews.templates.defaultSections.daily.challenges.title'), description: t('pages.reviews.templates.defaultSections.daily.challenges.description'), placeholder: t('pages.reviews.templates.defaultSections.daily.challenges.placeholder'), required: false },
    { id: 'learnings', title: t('pages.reviews.templates.defaultSections.daily.learnings.title'), description: t('pages.reviews.templates.defaultSections.daily.learnings.description'), placeholder: t('pages.reviews.templates.defaultSections.daily.learnings.placeholder'), required: false },
    { id: 'tomorrow', title: t('pages.reviews.templates.defaultSections.daily.tomorrow.title'), description: t('pages.reviews.templates.defaultSections.daily.tomorrow.description'), placeholder: t('pages.reviews.templates.defaultSections.daily.tomorrow.placeholder'), required: true }
  ],
  weekly: [
    { id: 'achievements', title: t('pages.reviews.templates.defaultSections.weekly.achievements.title'), description: t('pages.reviews.templates.defaultSections.weekly.achievements.description'), placeholder: t('pages.reviews.templates.defaultSections.weekly.achievements.placeholder'), required: true },
    { id: 'challenges', title: t('pages.reviews.templates.defaultSections.weekly.challenges.title'), description: t('pages.reviews.templates.defaultSections.weekly.challenges.description'), placeholder: t('pages.reviews.templates.defaultSections.weekly.challenges.placeholder'), required: false },
    { id: 'learnings', title: t('pages.reviews.templates.defaultSections.weekly.learnings.title'), description: t('pages.reviews.templates.defaultSections.weekly.learnings.description'), placeholder: t('pages.reviews.templates.defaultSections.weekly.learnings.placeholder'), required: false },
    { id: 'nextWeek', title: t('pages.reviews.templates.defaultSections.weekly.nextWeek.title'), description: t('pages.reviews.templates.defaultSections.weekly.nextWeek.description'), placeholder: t('pages.reviews.templates.defaultSections.weekly.nextWeek.placeholder'), required: true }
  ],
  monthly: [
    { id: 'progress', title: t('pages.reviews.templates.defaultSections.monthly.progress.title'), description: t('pages.reviews.templates.defaultSections.monthly.progress.description'), placeholder: t('pages.reviews.templates.defaultSections.monthly.progress.placeholder'), required: true },
    { id: 'highlights', title: t('pages.reviews.templates.defaultSections.monthly.highlights.title'), description: t('pages.reviews.templates.defaultSections.monthly.highlights.description'), placeholder: t('pages.reviews.templates.defaultSections.monthly.highlights.placeholder'), required: true },
    { id: 'challenges', title: t('pages.reviews.templates.defaultSections.monthly.challenges.title'), description: t('pages.reviews.templates.defaultSections.monthly.challenges.description'), placeholder: t('pages.reviews.templates.defaultSections.monthly.challenges.placeholder'), required: false },
    { id: 'nextMonth', title: t('pages.reviews.templates.defaultSections.monthly.nextMonth.title'), description: t('pages.reviews.templates.defaultSections.monthly.nextMonth.description'), placeholder: t('pages.reviews.templates.defaultSections.monthly.nextMonth.placeholder'), required: true }
  ]
})

export function ReviewTemplateManager({ onTemplateSelect }: ReviewTemplateManagerProps) {
  const { t, language } = useLanguage()

  // 获取模板的国际化名称和描述
  const getI18nTemplateInfo = (template: ReviewTemplate) => {
    if (template.isSystem) {
      const i18nData = reviewTemplatesI18n[language]
      const typeData = i18nData[template.type as keyof typeof i18nData]
      if (typeData) {
        return {
          name: typeData.name || template.name,
          description: typeData.description || template.description
        }
      }
    }
    return {
      name: template.name,
      description: template.description
    }
  }

  // 获取章节的国际化信息
  const getI18nSectionInfo = (sectionId: string, templateType: string) => {
    const i18nData = reviewTemplatesI18n[language]
    const typeData = i18nData[templateType as keyof typeof i18nData]

    if (typeData && typeData.sections && typeData.sections[sectionId as keyof typeof typeData.sections]) {
      const sectionData = typeData.sections[sectionId as keyof typeof typeData.sections]
      return {
        title: sectionData.title || '',
        description: sectionData.description || '',
        placeholder: sectionData.placeholder || ''
      }
    }

    return {
      title: '',
      description: '',
      placeholder: ''
    }
  }

  // 判断是否为系统模板（不允许修改章节ID）
  const isSystemTemplate = () => {
    // 如果正在编辑系统模板
    if (editingTemplate?.isSystem) {
      return true
    }

    // 如果是创建新模板，且选择的是标准类型，也不允许修改章节ID
    // 因为这些章节ID需要与国际化配置匹配
    if (!editingTemplate && ['daily', 'weekly', 'monthly', 'quarterly', 'yearly'].includes(formData.type)) {
      return true
    }

    return false
  }

  const [templates, setTemplates] = useState<ReviewTemplate[]>([])
  const [loading, setLoading] = useState(true)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [isAdvancedEditorOpen, setIsAdvancedEditorOpen] = useState(false)
  const [editingTemplate, setEditingTemplate] = useState<ReviewTemplate | null>(null)

  const defaultSections = getDefaultSections(t)
  const [formData, setFormData] = useState<TemplateFormData>({
    name: '',
    description: '',
    type: 'weekly',
    structure: { sections: defaultSections.weekly },
    isDefault: false
  })

  useEffect(() => {
    loadTemplates()
  }, [])

  const loadTemplates = async () => {
    setLoading(true)
    try {
      const result = await databaseApi.getReviewTemplates()
      if (result.success) {
        setTemplates(result.data || [])
      } else {
        console.error('Failed to load templates:', result.error)
      }
    } catch (error) {
      console.error('Error loading templates:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleCreateTemplate = () => {
    const defaultSections = getDefaultSections(t)
    setEditingTemplate(null)
    setFormData({
      name: '',
      description: '',
      type: 'weekly',
      structure: { sections: defaultSections.weekly },
      isDefault: false
    })
    setIsDialogOpen(true)
  }

  const handleEditTemplate = (template: ReviewTemplate) => {
    setEditingTemplate(template)
    setFormData({
      name: template.name,
      description: template.description || '',
      type: template.type,
      structure: template.structure,
      isDefault: template.isDefault
    })
    setIsDialogOpen(true)
  }

  const handleTypeChange = (type: string) => {
    const defaultSections = getDefaultSections(t)
    setFormData(prev => ({
      ...prev,
      type,
      structure: { sections: defaultSections[type as keyof typeof defaultSections] || defaultSections.weekly }
    }))
  }

  const handleSaveTemplate = async () => {
    try {
      const templateData = {
        name: formData.name,
        description: formData.description,
        type: formData.type,
        structure: formData.structure,
        isDefault: formData.isDefault
      }

      let result
      if (editingTemplate) {
        const updateData: UpdateReviewTemplateRequest = {
          id: editingTemplate.id,
          updates: templateData
        }
        result = await databaseApi.updateReviewTemplate(updateData)
      } else {
        const createData: CreateReviewTemplateRequest = templateData
        result = await databaseApi.createReviewTemplate(createData)
      }

      if (result.success) {
        await loadTemplates()
        setIsDialogOpen(false)
      } else {
        console.error('Failed to save template:', result.error)
      }
    } catch (error) {
      console.error('Error saving template:', error)
    }
  }

  const handleDeleteTemplate = async (templateId: string) => {
    if (window.confirm(t('pages.reviews.templates.deleteConfirm'))) {
      try {
        const result = await databaseApi.deleteReviewTemplate(templateId)
        if (result.success) {
          setTemplates(prev => prev.filter(t => t.id !== templateId))
        } else {
          console.error('Failed to delete template:', result.error)
        }
      } catch (error) {
        console.error('Error deleting template:', error)
      }
    }
  }

  const handleDuplicateTemplate = (template: ReviewTemplate) => {
    setEditingTemplate(null)
    setFormData({
      name: `${template.name} (${t('pages.reviews.templates.copy')})`,
      description: template.description || '',
      type: template.type,
      structure: template.structure,
      isDefault: false
    })
    setIsDialogOpen(true)
  }

  const exportTemplate = (template: ReviewTemplate) => {
    const exportData = {
      name: template.name,
      description: template.description,
      type: template.type,
      structure: template.structure,
      version: '1.0',
      exportedAt: new Date().toISOString()
    }

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${template.name.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_template.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const importTemplate = () => {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = '.json'
    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0]
      if (!file) return

      try {
        const text = await file.text()
        const importData = JSON.parse(text)

        // Validate import data
        if (!importData.name || !importData.type || !importData.structure) {
          alert(t('pages.reviews.templates.invalidFormat'))
          return
        }

        // Set form data for import
        setEditingTemplate(null)
        setFormData({
          name: `${importData.name} (${t('pages.reviews.templates.imported')})`,
          description: importData.description || '',
          type: importData.type,
          structure: importData.structure,
          isDefault: false
        })
        setIsDialogOpen(true)
      } catch (error) {
        console.error('Failed to import template:', error)
        alert(t('pages.reviews.templates.importFailed'))
      }
    }
    input.click()
  }

  const addSection = () => {
    const newSection: TemplateSection = {
      id: `section_${Date.now()}`,
      title: t('pages.reviews.templates.newSectionTitle'),
      description: t('pages.reviews.templates.newSectionDescription'),
      placeholder: t('pages.reviews.templates.newSectionPlaceholder'),
      required: false
    }
    setFormData(prev => ({
      ...prev,
      structure: {
        sections: [...prev.structure.sections, newSection]
      }
    }))
  }

  const updateSection = (index: number, updates: Partial<TemplateSection>) => {
    setFormData(prev => ({
      ...prev,
      structure: {
        sections: prev.structure.sections.map((section, i) =>
          i === index ? { ...section, ...updates } : section
        )
      }
    }))
  }

  const removeSection = (index: number) => {
    setFormData(prev => ({
      ...prev,
      structure: {
        sections: prev.structure.sections.filter((_, i) => i !== index)
      }
    }))
  }

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader>
              <div className="h-4 bg-muted rounded w-1/3"></div>
              <div className="h-3 bg-muted rounded w-1/2"></div>
            </CardHeader>
          </Card>
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">{t('pages.reviews.templates.title')}</h2>
          <p className="text-muted-foreground">
            {t('pages.reviews.templates.description')}
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={importTemplate}>
            <Upload className="h-4 w-4 mr-2" />
            {t('pages.reviews.templates.import')}
          </Button>
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button onClick={handleCreateTemplate}>
                <Plus className="h-4 w-4 mr-2" />
                {t('pages.reviews.templates.newTemplate')}
              </Button>
            </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>
                {editingTemplate ? t('pages.reviews.templates.editTemplate') : t('pages.reviews.templates.createTemplate')}
              </DialogTitle>
              <DialogDescription>
                {t('pages.reviews.templates.advancedEditorDescription')}
              </DialogDescription>
            </DialogHeader>
            
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">{t('pages.reviews.templates.templateName')}</label>
                  <Input
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    placeholder={t('pages.reviews.templates.namePlaceholder')}
                  />
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium">{t('pages.reviews.templates.templateType')}</label>
                  <Select value={formData.type} onValueChange={handleTypeChange}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="daily">{t('pages.reviews.editor.types.daily')}</SelectItem>
                      <SelectItem value="weekly">{t('pages.reviews.editor.types.weekly')}</SelectItem>
                      <SelectItem value="monthly">{t('pages.reviews.editor.types.monthly')}</SelectItem>
                      <SelectItem value="quarterly">{t('pages.reviews.editor.types.quarterly')}</SelectItem>
                      <SelectItem value="yearly">{t('pages.reviews.editor.types.yearly')}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium">{t('pages.reviews.templates.templateDescription')}</label>
                <Textarea
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder={t('pages.reviews.templates.descriptionPlaceholder')}
                  rows={3}
                />
              </div>
              
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="isDefault"
                  checked={formData.isDefault}
                  onChange={(e) => setFormData(prev => ({ ...prev, isDefault: e.target.checked }))}
                />
                <label htmlFor="isDefault" className="text-sm font-medium">
                  {t('pages.reviews.templates.setAsDefaultForType', { typeName: t(`pages.reviews.editor.types.${formData.type}`) })}
                </label>
              </div>

              {/* Template Sections Preview */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <label className="text-sm font-medium">{t('pages.reviews.templates.sections')} ({formData.structure.sections.length})</label>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setIsAdvancedEditorOpen(true)}
                  >
                    <Settings className="h-4 w-4 mr-2" />
                    {t('pages.reviews.templates.advancedEditor')}
                  </Button>
                </div>
                <div className="max-h-32 overflow-y-auto border rounded p-2 bg-muted/50">
                  {formData.structure.sections.map((section, index) => (
                    <div key={section.id} className="text-xs text-muted-foreground mb-1">
                      {index + 1}. {section.title} {section.required && '*'}
                    </div>
                  ))}
                </div>
              </div>

              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                  {t('pages.reviews.editor.cancel')}
                </Button>
                <Button onClick={handleSaveTemplate} disabled={!formData.name}>
                  {editingTemplate ? t('common.update') : t('common.create')} {t('pages.reviews.templates.template')}
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>

        {/* Advanced Template Editor */}
        <Dialog open={isAdvancedEditorOpen} onOpenChange={setIsAdvancedEditorOpen}>
          <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto scrollbar-hide">
            <DialogHeader>
              <DialogTitle>{t('pages.reviews.templates.advancedEditor')}</DialogTitle>
              <DialogDescription>
                {t('pages.reviews.templates.advancedEditorDescription')}
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium">{t('pages.reviews.templates.sections')}</h3>
                <Button onClick={addSection} size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  {t('pages.reviews.templates.addSection')}
                </Button>
              </div>

              <div className="space-y-4">
                {formData.structure.sections.map((section, index) => {
                  // 获取国际化的章节信息作为占位符
                  const i18nSectionInfo = getI18nSectionInfo(section.id, formData.type)

                  return (
                    <Card key={section.id} className="p-4">
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">{t('pages.reviews.templates.sectionNumber', { number: index + 1 })}</span>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => removeSection(index)}
                            disabled={formData.structure.sections.length <= 1}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>

                        <div className="grid grid-cols-2 gap-3">
                          <div className="space-y-2">
                            <label className="text-xs font-medium">{t('pages.reviews.templates.sectionTitle')}</label>
                            <Input
                              value={section.title}
                              onChange={(e) => updateSection(index, { title: e.target.value })}
                              placeholder={i18nSectionInfo.title || t('pages.reviews.templates.sectionTitlePlaceholder')}
                            />
                          </div>

                          <div className="space-y-2">
                            <label className="text-xs font-medium">{t('pages.reviews.templates.sectionId')}</label>
                            <Input
                              value={section.id}
                              onChange={(e) => updateSection(index, { id: e.target.value })}
                              placeholder={t('pages.reviews.templates.sectionIdPlaceholder')}
                              disabled={isSystemTemplate()}
                              className={isSystemTemplate() ? 'bg-muted cursor-not-allowed' : ''}
                            />
                            {isSystemTemplate() && (
                              <p className="text-xs text-muted-foreground">
                                {t('pages.reviews.templates.systemTemplateIdReadonly')}
                              </p>
                            )}
                          </div>
                        </div>

                        <div className="space-y-2">
                          <label className="text-xs font-medium">{t('pages.reviews.templates.sectionDescription')}</label>
                          <Input
                            value={section.description}
                            onChange={(e) => updateSection(index, { description: e.target.value })}
                            placeholder={i18nSectionInfo.description || t('pages.reviews.templates.sectionDescriptionPlaceholder')}
                          />
                        </div>

                        <div className="space-y-2">
                          <label className="text-xs font-medium">{t('pages.reviews.templates.placeholder')}</label>
                          <Textarea
                            value={section.placeholder}
                            onChange={(e) => updateSection(index, { placeholder: e.target.value })}
                            placeholder={i18nSectionInfo.placeholder || t('pages.reviews.templates.placeholderPlaceholder')}
                            rows={2}
                          />
                        </div>

                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id={`required-${index}`}
                          checked={section.required}
                          onChange={(e) => updateSection(index, { required: e.target.checked })}
                        />
                        <label htmlFor={`required-${index}`} className="text-xs font-medium">
                          {t('pages.reviews.templates.required')}
                        </label>
                      </div>
                    </div>
                  </Card>
                  )
                })}
              </div>

              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setIsAdvancedEditorOpen(false)}>
                  {t('common.done')}
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
        </div>
      </div>

      {/* Templates List */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {templates.map((template) => {
          const i18nInfo = getI18nTemplateInfo(template)
          return (
            <Card key={template.id} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="text-lg flex items-center gap-2">
                      {i18nInfo.name}
                      {template.isDefault && <Star className="h-4 w-4 text-yellow-500 fill-current" />}
                      {template.isSystem && <Badge variant="outline" className="text-xs">{t('pages.reviews.templates.systemTemplate')}</Badge>}
                    </CardTitle>
                    <CardDescription className="mt-1">
                      <Badge variant="secondary" className="mr-2">
                        {t(`pages.reviews.editor.types.${template.type}`)}
                      </Badge>
                      {i18nInfo.description}
                    </CardDescription>
                  </div>
                <div className="flex gap-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => exportTemplate(template)}
                    title={t('pages.reviews.templates.export')}
                  >
                    <Download className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleDuplicateTemplate(template)}
                    title={t('pages.reviews.templates.duplicate')}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                  {!template.isSystem && (
                    <>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleEditTemplate(template)}
                        title={t('pages.reviews.templates.edit')}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteTemplate(template.id)}
                        title={t('pages.reviews.templates.delete')}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </>
                  )}
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">
                  {t('pages.reviews.templates.sectionsCount', { count: template.structure?.sections?.length || 0 })}
                </p>
                {onTemplateSelect && (
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full"
                    onClick={() => onTemplateSelect(template)}
                  >
                    {t('pages.reviews.templates.useTemplate')}
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
          )
        })}
      </div>
    </div>
  )
}

export default ReviewTemplateManager
