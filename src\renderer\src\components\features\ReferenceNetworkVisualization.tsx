import React, { useEffect, useRef, useState } from 'react'
import { NetworkGraph, NetworkNode, NetworkEdge, referenceNetworkAnalyzer } from '../../services/referenceNetworkAnalyzer'
import { UnifiedReference } from '../../services/unifiedReferenceService'

interface ReferenceNetworkVisualizationProps {
  references: UnifiedReference[]
  onNodeClick?: (nodeId: string) => void
  className?: string
}

/**
 * 引用网络可视化组件
 * 使用 Canvas 绘制网络图
 */
export const ReferenceNetworkVisualization: React.FC<ReferenceNetworkVisualizationProps> = ({
  references,
  onNodeClick,
  className = ''
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const [networkGraph, setNetworkGraph] = useState<NetworkGraph | null>(null)
  const [selectedNode, setSelectedNode] = useState<string | null>(null)
  const [hoveredNode, setHoveredNode] = useState<string | null>(null)
  const [isDragging, setIsDragging] = useState(false)
  const [dragNode, setDragNode] = useState<string | null>(null)

  // 节点位置状态
  const [nodePositions, setNodePositions] = useState<Map<string, { x: number; y: number }>>(new Map())

  // 构建网络图
  useEffect(() => {
    if (references.length === 0) {
      setNetworkGraph(null)
      return
    }

    console.log('🕸️ 构建引用网络图...')
    const graph = referenceNetworkAnalyzer.buildNetworkGraph(references)
    setNetworkGraph(graph)

    // 初始化节点位置（圆形布局）
    const positions = new Map<string, { x: number; y: number }>()
    const centerX = 300
    const centerY = 200
    const radius = Math.min(150, 50 + graph.nodes.length * 5)

    graph.nodes.forEach((node, index) => {
      const angle = (2 * Math.PI * index) / graph.nodes.length
      positions.set(node.id, {
        x: centerX + radius * Math.cos(angle),
        y: centerY + radius * Math.sin(angle)
      })
    })

    setNodePositions(positions)
  }, [references])

  // 绘制网络图
  useEffect(() => {
    if (!networkGraph || !canvasRef.current) return

    const canvas = canvasRef.current
    const ctx = canvas.getContext('2d')
    if (!ctx) return

    // 清空画布
    ctx.clearRect(0, 0, canvas.width, canvas.height)

    // 绘制边
    networkGraph.edges.forEach(edge => {
      const sourcePos = nodePositions.get(edge.source)
      const targetPos = nodePositions.get(edge.target)
      
      if (sourcePos && targetPos) {
        ctx.beginPath()
        ctx.moveTo(sourcePos.x, sourcePos.y)
        ctx.lineTo(targetPos.x, targetPos.y)
        
        // 边的样式
        ctx.strokeStyle = edge.color + '80' // 半透明
        ctx.lineWidth = Math.max(1, edge.weight * 3)
        ctx.stroke()

        // 绘制边标签（可选）
        if (edge.weight > 0.8) {
          const midX = (sourcePos.x + targetPos.x) / 2
          const midY = (sourcePos.y + targetPos.y) / 2
          
          ctx.fillStyle = '#666'
          ctx.font = '10px Arial'
          ctx.textAlign = 'center'
          ctx.fillText(edge.type, midX, midY)
        }
      }
    })

    // 绘制节点
    networkGraph.nodes.forEach(node => {
      const pos = nodePositions.get(node.id)
      if (!pos) return

      const isSelected = selectedNode === node.id
      const isHovered = hoveredNode === node.id
      const radius = Math.max(8, Math.min(20, node.size * 2))

      // 节点圆圈
      ctx.beginPath()
      ctx.arc(pos.x, pos.y, radius, 0, 2 * Math.PI)
      
      // 节点填充
      ctx.fillStyle = isSelected ? '#ff6b6b' : isHovered ? '#4ecdc4' : node.color
      ctx.fill()
      
      // 节点边框
      ctx.strokeStyle = isSelected || isHovered ? '#333' : '#fff'
      ctx.lineWidth = isSelected || isHovered ? 2 : 1
      ctx.stroke()

      // 中心性指示器
      if (node.centrality > 0.5) {
        ctx.beginPath()
        ctx.arc(pos.x, pos.y, radius + 3, 0, 2 * Math.PI)
        ctx.strokeStyle = '#ffd93d'
        ctx.lineWidth = 2
        ctx.stroke()
      }

      // 节点标签
      ctx.fillStyle = '#333'
      ctx.font = `${isSelected || isHovered ? '12px' : '10px'} Arial`
      ctx.textAlign = 'center'
      ctx.fillText(
        node.label.length > 10 ? node.label.substring(0, 10) + '...' : node.label,
        pos.x,
        pos.y + radius + 15
      )
    })
  }, [networkGraph, nodePositions, selectedNode, hoveredNode])

  // 处理鼠标事件
  const handleMouseMove = (event: React.MouseEvent<HTMLCanvasElement>) => {
    if (!networkGraph || !canvasRef.current) return

    const canvas = canvasRef.current
    const rect = canvas.getBoundingClientRect()
    const mouseX = event.clientX - rect.left
    const mouseY = event.clientY - rect.top

    // 检查是否悬停在节点上
    let foundNode: string | null = null
    networkGraph.nodes.forEach(node => {
      const pos = nodePositions.get(node.id)
      if (pos) {
        const distance = Math.sqrt((mouseX - pos.x) ** 2 + (mouseY - pos.y) ** 2)
        const radius = Math.max(8, Math.min(20, node.size * 2))
        if (distance <= radius) {
          foundNode = node.id
        }
      }
    })

    setHoveredNode(foundNode)

    // 处理拖拽
    if (isDragging && dragNode) {
      setNodePositions(prev => {
        const newPositions = new Map(prev)
        newPositions.set(dragNode, { x: mouseX, y: mouseY })
        return newPositions
      })
    }
  }

  const handleMouseDown = (event: React.MouseEvent<HTMLCanvasElement>) => {
    if (!networkGraph || !canvasRef.current) return

    const canvas = canvasRef.current
    const rect = canvas.getBoundingClientRect()
    const mouseX = event.clientX - rect.left
    const mouseY = event.clientY - rect.top

    // 检查点击的节点
    networkGraph.nodes.forEach(node => {
      const pos = nodePositions.get(node.id)
      if (pos) {
        const distance = Math.sqrt((mouseX - pos.x) ** 2 + (mouseY - pos.y) ** 2)
        const radius = Math.max(8, Math.min(20, node.size * 2))
        if (distance <= radius) {
          setSelectedNode(node.id)
          setDragNode(node.id)
          setIsDragging(true)
          onNodeClick?.(node.id)
        }
      }
    })
  }

  const handleMouseUp = () => {
    setIsDragging(false)
    setDragNode(null)
  }

  if (!networkGraph || networkGraph.nodes.length === 0) {
    return (
      <div className={`reference-network-visualization ${className}`}>
        <div className="flex items-center justify-center h-64 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
          <div className="text-center text-gray-500">
            <div className="text-4xl mb-2">🕸️</div>
            <div>暂无引用网络数据</div>
            <div className="text-sm mt-1">添加一些引用后将显示网络图</div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={`reference-network-visualization ${className}`}>
      {/* 网络指标面板 */}
      <div className="network-metrics mb-4 p-3 bg-gray-50 rounded-lg">
        <h3 className="font-medium text-gray-800 mb-2">网络指标</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-sm">
          <div className="metric-item">
            <div className="text-gray-600">节点数</div>
            <div className="font-medium text-blue-600">{networkGraph.metrics.totalNodes}</div>
          </div>
          <div className="metric-item">
            <div className="text-gray-600">连接数</div>
            <div className="font-medium text-green-600">{networkGraph.metrics.totalEdges}</div>
          </div>
          <div className="metric-item">
            <div className="text-gray-600">网络密度</div>
            <div className="font-medium text-purple-600">{(networkGraph.metrics.density * 100).toFixed(1)}%</div>
          </div>
          <div className="metric-item">
            <div className="text-gray-600">聚类数</div>
            <div className="font-medium text-orange-600">{networkGraph.clusters.length}</div>
          </div>
        </div>
      </div>

      {/* 网络图画布 */}
      <div className="network-canvas-container bg-white border rounded-lg p-4">
        <canvas
          ref={canvasRef}
          width={600}
          height={400}
          className="border rounded cursor-pointer"
          onMouseMove={handleMouseMove}
          onMouseDown={handleMouseDown}
          onMouseUp={handleMouseUp}
          onMouseLeave={() => {
            setHoveredNode(null)
            setIsDragging(false)
            setDragNode(null)
          }}
        />
      </div>

      {/* 图例 */}
      <div className="network-legend mt-4 p-3 bg-gray-50 rounded-lg">
        <h4 className="font-medium text-gray-800 mb-2">图例</h4>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-3 text-sm">
          <div className="legend-section">
            <div className="font-medium text-gray-700 mb-1">节点类型</div>
            <div className="space-y-1">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 rounded-full bg-blue-500"></div>
                <span>文档</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 rounded-full bg-green-500"></div>
                <span>项目</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 rounded-full bg-purple-500"></div>
                <span>领域</span>
              </div>
            </div>
          </div>
          
          <div className="legend-section">
            <div className="font-medium text-gray-700 mb-1">连接类型</div>
            <div className="space-y-1">
              <div className="flex items-center space-x-2">
                <div className="w-4 h-0.5 bg-blue-500"></div>
                <span>WikiLink</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-4 h-0.5 bg-green-500"></div>
                <span>项目引用</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-4 h-0.5 bg-purple-500"></div>
                <span>领域引用</span>
              </div>
            </div>
          </div>
          
          <div className="legend-section">
            <div className="font-medium text-gray-700 mb-1">特殊标记</div>
            <div className="space-y-1">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 rounded-full border-2 border-yellow-400"></div>
                <span>中心节点</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 rounded-full bg-red-500"></div>
                <span>选中节点</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 选中节点信息 */}
      {selectedNode && (
        <div className="selected-node-info mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <h4 className="font-medium text-blue-800 mb-2">选中节点信息</h4>
          {(() => {
            const node = networkGraph.nodes.find(n => n.id === selectedNode)
            if (!node) return null
            
            return (
              <div className="text-sm">
                <div className="grid grid-cols-2 gap-2">
                  <div><span className="text-gray-600">名称:</span> {node.label}</div>
                  <div><span className="text-gray-600">类型:</span> {node.type}</div>
                  <div><span className="text-gray-600">连接数:</span> {node.size - 1}</div>
                  <div><span className="text-gray-600">中心性:</span> {(node.centrality * 100).toFixed(1)}%</div>
                </div>
              </div>
            )
          })()}
        </div>
      )}
    </div>
  )
}

export default ReferenceNetworkVisualization
