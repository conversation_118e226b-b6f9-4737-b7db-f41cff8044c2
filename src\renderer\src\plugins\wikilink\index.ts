import { $ctx, $inputRule, $node, $remark, $prose } from '@milkdown/kit/utils'
import { InputRule } from '@milkdown/kit/prose/inputrules'
import { Plugin, PluginKey } from '@milkdown/kit/prose/state'
import { wikLinkSchema } from './schema'
import { wikLinkParser } from './parser'
import { WikiLinkNodeView } from './renderer'
import { createPreviewTooltip } from './preview'

// WikiLink 插件配置
export interface WikiLinkConfig {
  /** 是否启用自动补全 */
  enableAutoComplete: boolean
  /** 是否启用悬停预览 */
  enablePreview: boolean
  /** 是否启用双向链接 */
  enableBidirectionalLinks: boolean
  /** 预览最大行数 */
  previewMaxLines: number
  /** 是否自动创建页面 */
  autoCreatePages: boolean
  /** 读取页面内容的函数 */
  readPageContent?: (pageName: string) => Promise<string>
  /** 保存页面内容的函数 */
  savePageContent?: (pageName: string, content: string) => Promise<void>
  /** 页面点击处理函数 */
  onPageClick?: (pageName: string) => void
  /** 链接创建回调 */
  onLinkCreate?: (source: string, target: string) => void
}

// 默认配置
export const defaultWikiLinkConfig: WikiLinkConfig = {
  enableAutoComplete: true,
  enablePreview: true,
  enableBidirectionalLinks: true,
  previewMaxLines: 5,
  autoCreatePages: false,
}

// WikiLink 配置上下文
export const wikiLinkConfigCtx = $ctx(defaultWikiLinkConfig, 'wikiLinkConfig')

// WikiLink 节点定义
export const wikiLinkNode = $node('wikilink', (ctx) => {
  console.log('🏗️ WikiLink 节点定义初始化...')
  console.log('📋 使用的 schema:', wikLinkSchema)
  return {
    ...wikLinkSchema,
    parseMarkdown: {
      match: (node: any) => node.type === 'wikilink',
      runner: (state: any, node: any, type: any) => {
        console.log('🔍 parseMarkdown 处理 WikiLink 节点:', node)
        // 从 Remark AST 节点的 data 属性中获取信息
        const target = node.data?.target || ''
        const display = node.data?.display || target
        console.log('📝 解析的属性:', { target, display })
        state.addNode(type, { target, display, valid: true })
      }
    },
    toMarkdown: {
      match: (node: any) => node.type.name === 'wikilink',
      runner: (state: any, node: any) => {
        const { target, display } = node.attrs
        if (!display || display === target) {
          state.addNode('text', undefined, `[[${target}]]`)
        } else {
          state.addNode('text', undefined, `[[${target}|${display}]]`)
        }
      }
    }
  }
})

// WikiLink Remark 插件 - 处理 Markdown 解析和序列化
export const wikiLinkRemarkPlugin = $remark('wikiLinkRemark', () => {
  console.log('📝 WikiLink Remark 插件初始化...')

  // 返回一个插件数组，包含解析器和自定义序列化器
  return [
    wikLinkParser,
    // 自定义序列化器，确保 WikiLink 不被转义
    function wikiLinkSerializer() {
      return function transformer(tree: any) {
        // 在序列化前清理转义字符
        function visit(node: any) {
          if (node.type === 'text' && node.value) {
            // 清理 WikiLink 相关的转义字符
            node.value = node.value
              .replace(/\\\[\\\[/g, '[[')
              .replace(/\\\]\\\]/g, ']]')
              .replace(/\\\[/g, '[')
              .replace(/\\\]/g, ']')
          }

          if (node.children) {
            node.children.forEach(visit)
          }
        }

        visit(tree)
      }
    }
  ] as any
})

// 导入智能输入规则
import {
  singleBracketInputRule,
  doubleBracketInputRule,
  doubleBracketKeymap,
  smartBackspaceInputRule,
  cleanContent
} from './smartInput'

// WikiLink 完成输入规则 - 当用户完成 [[内容]] 输入时触发
export const wikiLinkCompletionRule = $inputRule((ctx) => {
  console.log('🎯 WikiLink 完成规则初始化...')
  const config = ctx.get(wikiLinkConfigCtx.key)
  console.log('📋 获取到的配置:', config)

  return new InputRule(
    /\[\[([^\]]+?)(\|([^\]]+?))?\]\]$/,  // 使用非贪婪匹配，至少1个字符
    (state, match, start, end) => {
      console.log('🔍 WikiLink 完成规则触发!', { match, start, end })
      const [fullMatch, target, , display] = match
      console.log('📝 解析的目标和显示文本:', { fullMatch, target, display })
      console.log('🔍 匹配位置的实际文本:', state.doc.textBetween(start, end))

      // 检查目标有效性 - 降低要求，允许单字符
      const trimmedTarget = target.trim()
      if (!trimmedTarget || trimmedTarget.length === 0) {
        console.log('⏭️ 跳过 WikiLink 转换（目标为空）', { target, trimmedTarget })
        return null
      }

      const { tr } = state
      const node = state.schema.nodes.wikilink.create({
        target: trimmedTarget,
        display: display?.trim() || trimmedTarget,
        valid: true
      })
      console.log('🏗️ 创建的 WikiLink 节点:', node)

      // 触发链接创建回调
      if (config.onLinkCreate) {
        console.log('🔗 触发链接创建回调')
        config.onLinkCreate('current-page', trimmedTarget)
      }

      const result = tr.replaceWith(start, end, node)
      console.log('✅ WikiLink 节点替换完成')
      return result
    }
  )
})

// 智能输入规则集合 - 专注于输入规则
export const smartInputRules = [
  doubleBracketInputRule,    // 处理 [[ 输入自动补全
  wikiLinkCompletionRule,    // 处理完整的 [[内容]] 转换为链接
  // singleBracketInputRule, // 暂时禁用，避免冲突
  smartBackspaceInputRule    // 处理智能退格
]



// WikiLink 预览插件 - 使用 $prose 包装
export const wikiLinkPreviewPlugin = $prose((ctx) => {
  console.log('👁️ WikiLink 预览插件初始化...')
  const config = ctx.get(wikiLinkConfigCtx.key)
  return createPreviewTooltip(config)
})

// WikiLink 视图插件 - 注入节点视图渲染器
export const wikiLinkViewPlugin = $prose((ctx) => {
  console.log('🎨 WikiLink 视图插件初始化...')
  const config = ctx.get(wikiLinkConfigCtx.key)

  return new Plugin({
    key: new PluginKey('wikilink-view'),
    props: {
      nodeViews: {
        wikilink: (node, view, getPos) => {
          console.log('🏗️ 创建 WikiLink 节点视图:', node.attrs)
          return new WikiLinkNodeView(node, view, getPos as () => number, config)
        }
      }
    }
  })
})

// WikiLink 插件集合 (使用新的智能输入规则)
export const wikiLink = [
  wikiLinkConfigCtx,
  wikiLinkNode,
  wikiLinkRemarkPlugin,        // 启用 Remark 解析，处理 Markdown 持久化
  ...smartInputRules,          // 使用智能输入规则集合
  // wikiLinkAutoCompletePlugin,  // 暂时禁用，类型问题
  wikiLinkPreviewPlugin,       // 启用预览插件
  wikiLinkViewPlugin,          // 启用视图插件，提供点击功能
]

export default wikiLink
