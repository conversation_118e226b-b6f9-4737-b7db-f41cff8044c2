<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tailwind CSS 浮动输入框</title>
    <!-- 引入 Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* 自定义字体，让界面更美观 */
        body {
            font-family: 'Inter', sans-serif;
        }
        /* 为输入框容器添加一个简单的过渡动画 */
        #modal-container.hidden {
            display: none;
        }
        .modal-content {
            transition: all 0.3s ease-out;
        }
        #modal-container:not(.hidden) .modal-content {
            transform: translateY(0);
            opacity: 1;
        }
        #modal-container.hiding .modal-content {
            transform: translateY(-20px);
            opacity: 0;
        }
    </style>
     <!-- 引入 Google Fonts 的 Inter 字体 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="bg-gray-100 flex items-center justify-center h-screen">

    <!-- 触发按钮 -->
    <button id="open-modal-btn" class="px-6 py-3 bg-blue-600 text-white font-semibold rounded-lg shadow-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-75 transition-transform transform hover:scale-105">
        打开输入框
    </button>

    <!-- 浮动输入框容器 (修改了这里的样式) -->
    <div id="modal-container" class="hidden fixed top-5 left-1/2 -translate-x-1/2 w-full max-w-md px-4 z-50">
        <!-- 输入框内容 -->
        <div id="modal-content" class="modal-content bg-white w-full p-4 rounded-2xl shadow-2xl relative transform -translate-y-10 opacity-0">
            <!-- 唯一的输入框 -->
            <input type="text" id="feedback-input" placeholder="输入后按 Enter 键确认..." class="w-full text-lg px-4 py-3 border-0 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition">
        </div>
    </div>

    <script>
        // 获取 DOM 元素
        const openModalBtn = document.getElementById('open-modal-btn');
        const modalContainer = document.getElementById('modal-container');
        const modalContent = document.getElementById('modal-content');
        const feedbackInput = document.getElementById('feedback-input');

        // 打开输入框的函数
        const openModal = () => {
            modalContainer.classList.remove('hidden');
            // 自动聚焦到输入框
            setTimeout(() => feedbackInput.focus(), 50); 
            // 触发动画
            setTimeout(() => modalContainer.classList.remove('hiding'), 0);
        };

        // 关闭输入框的函数
        const closeModal = () => {
            modalContainer.classList.add('hiding');
            // 等待动画结束后再隐藏
            modalContainer.addEventListener('transitionend', () => {
                modalContainer.classList.add('hidden');
                feedbackInput.value = ''; // 清空输入框
            }, { once: true });
        };

        // --- 事件监听 (修改了这里的逻辑) ---

        // 点击“打开”按钮
        openModalBtn.addEventListener('click', (event) => {
            event.stopPropagation(); // 防止点击事件冒泡到 document，导致逻辑冲突
            if (modalContainer.classList.contains('hidden')) {
                openModal();
            }
        });

        // 点击输入框内容区域，防止冒泡导致关闭
        modalContent.addEventListener('click', (event) => {
            event.stopPropagation();
        });

        // 点击页面任何其他地方，都会关闭输入框
        document.addEventListener('click', () => {
            if (!modalContainer.classList.contains('hidden')) {
                closeModal();
            }
        });
        
        // 监听输入框的键盘事件
        feedbackInput.addEventListener('keydown', (event) => {
            // 如果按下的是 Enter 键
            if (event.key === 'Enter') {
                event.preventDefault(); // 防止表单提交等默认行为
                const input = feedbackInput.value;
                if (input.trim() !== '') { // 确保输入不为空
                    console.log('输入的内容:', input); // 你可以在这里处理输入的数据
                }
                closeModal();
            }
        });

        // 按下 Escape 键关闭输入框
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Escape' && !modalContainer.classList.contains('hidden')) {
                closeModal();
            }
        });
    </script>

</body>
</html>
