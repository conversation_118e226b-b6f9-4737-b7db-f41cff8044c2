import { useState } from 'react'
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '../ui/dialog'
import { Button } from '../ui/button'
import { Textarea } from '../ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select'
import { Input } from '../ui/input'
import { Badge } from '../ui/badge'
import { cn } from '../../lib/utils'
import { useLanguage } from '../../contexts/LanguageContext'
import type { InboxItem } from './InboxItem'

interface QuickCaptureDialogProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (itemData: Omit<InboxItem, 'id' | 'createdAt' | 'updatedAt'>) => void
}

export function QuickCaptureDialog({
  isOpen,
  onClose,
  onSubmit
}: QuickCaptureDialogProps) {
  const [formData, setFormData] = useState({
    content: '',
    tags: [] as string[],
    processed: false
  })
  const [tagInput, setTagInput] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { t } = useLanguage()

  // 常用标签选项
  const commonTags = ['工作', '学习', '生活', '想法', '待办', '重要']

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!formData.content.trim()) return

    setIsSubmitting(true)
    try {
      const itemData: Omit<InboxItem, 'id' | 'createdAt' | 'updatedAt'> = {
        content: formData.content.trim(),
        type: 'idea', // 统一设置为灵感类型
        priority: 'medium', // 默认中等优先级
        tags: formData.tags,
        processed: false
      }

      await onSubmit(itemData)
      onClose()

      // Reset form
      setFormData({
        content: '',
        tags: [],
        processed: false
      })
      setTagInput('')
    } catch (error) {
      console.error('Failed to create inbox item:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleClose = () => {
    if (!isSubmitting) {
      onClose()
    }
  }

  const handleAddTag = () => {
    const tag = tagInput.trim().toLowerCase()
    if (tag && !formData.tags.includes(tag)) {
      setFormData((prev) => ({
        ...prev,
        tags: [...prev.tags, tag]
      }))
      setTagInput('')
    }
  }

  const handleAddCommonTag = (tag: string) => {
    if (!formData.tags.includes(tag)) {
      setFormData((prev) => ({
        ...prev,
        tags: [...prev.tags, tag]
      }))
    }
  }

  const handleRemoveTag = (tagToRemove: string) => {
    setFormData((prev) => ({
      ...prev,
      tags: prev.tags.filter((tag) => tag !== tagToRemove)
    }))
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && (e.metaKey || e.ctrlKey)) {
      e.preventDefault()
      handleSubmit(e as any)
    }
  }



  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-lg">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <div className="w-8 h-8 rounded-lg bg-yellow-100 flex items-center justify-center">
              <span className="text-yellow-700 font-semibold">💡</span>
            </div>
            快速记录灵感
          </DialogTitle>
          <DialogDescription>
            记录你的想法、创意和灵感，稍后可以进一步处理和整理
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Content */}
          <div className="space-y-2">
            <Textarea
              value={formData.content}
              onChange={(e) => setFormData((prev) => ({ ...prev, content: e.target.value }))}
              onKeyDown={handleKeyDown}
              placeholder="记录你的想法、创意或灵感..."
              rows={4}
              className="resize-none"
              required
              autoFocus
            />
            <div className="text-xs text-muted-foreground">{t('pages.inbox.quickCaptureDialog.saveHint')}</div>
          </div>



          {/* Tags */}
          <div className="space-y-3">
            <label className="text-sm font-medium">{t('pages.inbox.quickCaptureDialog.tagsLabel')}</label>

            {/* 常用标签快捷按钮 */}
            <div className="space-y-2">
              <div className="text-xs text-muted-foreground">常用标签：</div>
              <div className="flex flex-wrap gap-1">
                {commonTags.map((tag) => (
                  <Button
                    key={tag}
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => handleAddCommonTag(tag)}
                    disabled={formData.tags.includes(tag)}
                    className="h-7 px-2 text-xs"
                  >
                    #{tag}
                  </Button>
                ))}
              </div>
            </div>

            {/* 自定义标签输入 */}
            <div className="flex gap-2">
              <Input
                value={tagInput}
                onChange={(e) => setTagInput(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault()
                    handleAddTag()
                  }
                }}
                placeholder={t('pages.inbox.quickCaptureDialog.addTagPlaceholder')}
                className="flex-1"
              />
              <Button type="button" variant="outline" size="sm" onClick={handleAddTag}>
                {t('pages.inbox.quickCaptureDialog.addButton')}
              </Button>
            </div>

            {formData.tags.length > 0 && (
              <div className="flex flex-wrap gap-1">
                {formData.tags.map((tag) => (
                  <Badge
                    key={tag}
                    variant="secondary"
                    className="text-xs cursor-pointer hover:bg-destructive hover:text-destructive-foreground"
                    onClick={() => handleRemoveTag(tag)}
                  >
                    #{tag} ×
                  </Badge>
                ))}
              </div>
            )}
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleClose} disabled={isSubmitting}>
              取消
            </Button>
            <Button type="submit" disabled={!formData.content.trim() || isSubmitting}>
              {isSubmitting ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                  <span>记录中...</span>
                </div>
              ) : (
                '记录灵感'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}

export default QuickCaptureDialog
