import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { Progress } from '../ui/progress'
import { But<PERSON> } from '../ui/button'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from '../ui/tabs'
import { TrendingUp, TrendingDown, Minus, Target, Lightbulb, AlertTriangle, CheckCircle, Brain } from 'lucide-react'
import { useLanguage } from '../../contexts/LanguageContext'
import { databaseApi } from '../../lib/api'

interface ReviewAnalysisProps {
  type: string
  period: string
  onClose?: () => void
}

interface AnalysisData {
  trends: {
    projectCompletion: { current: number; previous: number; trend: 'up' | 'down' | 'stable'; change: number }
    taskProductivity: { current: number; previous: number; trend: 'up' | 'down' | 'stable'; change: number }
    habitConsistency: { current: number; previous: number; trend: 'up' | 'down' | 'stable'; change: number }
  }
  predictions: {
    nextPeriodProjections: { expectedProjects: number; expectedTasks: number; riskFactors: string[] }
    goalAchievementProbability: number
    recommendedFocus: string[]
  }
  recommendations: {
    immediate: string[]
    shortTerm: string[]
    longTerm: string[]
  }
  goalProgress: {
    overallScore: number
    categoryScores: { projects: number; habits: number; kpis: number }
    achievements: string[]
    gaps: string[]
  }
}

export function ReviewAnalysis({ type, period, onClose }: ReviewAnalysisProps) {
  const { t } = useLanguage()
  const [loading, setLoading] = useState(true)
  const [analysisData, setAnalysisData] = useState<AnalysisData | null>(null)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    loadAnalysis()
  }, [type, period])

  const loadAnalysis = async () => {
    setLoading(true)
    setError(null)
    try {
      const result = await databaseApi.getReviewAnalysis(type, period)
      if (result.success) {
        setAnalysisData(result.data)
      } else {
        setError(result.error || t('pages.reviews.analysis.loadFailed'))
      }
    } catch (err) {
      setError(t('pages.reviews.analysis.loadError'))
      console.error('Error loading analysis:', err)
    } finally {
      setLoading(false)
    }
  }

  const getTrendIcon = (trend: 'up' | 'down' | 'stable') => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="h-4 w-4 text-green-500" />
      case 'down':
        return <TrendingDown className="h-4 w-4 text-red-500" />
      case 'stable':
        return <Minus className="h-4 w-4 text-gray-500" />
    }
  }

  const getTrendColor = (trend: 'up' | 'down' | 'stable') => {
    switch (trend) {
      case 'up':
        return 'text-green-600'
      case 'down':
        return 'text-red-600'
      case 'stable':
        return 'text-gray-600'
    }
  }

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600'
    if (score >= 60) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getProgressColor = (score: number) => {
    if (score >= 80) return 'bg-green-500'
    if (score >= 60) return 'bg-yellow-500'
    return 'bg-red-500'
  }

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <Brain className="h-6 w-6" />
            {t('pages.reviews.analysis.title')}
          </h2>
          {onClose && (
            <Button variant="outline" onClick={onClose}>
              {t('common.close')}
            </Button>
          )}
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-muted rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-muted rounded w-full"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (error || !analysisData) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <Brain className="h-6 w-6" />
            {t('pages.reviews.analysis.title')}
          </h2>
          {onClose && (
            <Button variant="outline" onClick={onClose}>
              {t('common.close')}
            </Button>
          )}
        </div>
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-8">
              <AlertTriangle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">{t('pages.reviews.analysis.analysisUnavailable')}</h3>
              <p className="text-muted-foreground mb-4">
                {error || t('pages.reviews.analysis.unableToGenerate')}
              </p>
              <Button onClick={loadAnalysis}>
                {t('pages.reviews.analysis.tryAgain')}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <Brain className="h-6 w-6" />
            {t('pages.reviews.analysis.title')}
          </h2>
          <p className="text-muted-foreground">
            {t('pages.reviews.analysis.description')} {t(`pages.reviews.editor.types.${type}`)} {t('pages.reviews.analysis.reviewFor')} - {period}
          </p>
        </div>
        {onClose && (
          <Button variant="outline" onClick={onClose}>
            {t('common.close')}
          </Button>
        )}
      </div>

      {/* Overall Score */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            {t('pages.reviews.analysis.overallScore')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <div className="text-4xl font-bold">
              <span className={getScoreColor(analysisData.goalProgress.overallScore)}>
                {analysisData.goalProgress.overallScore}
              </span>
              <span className="text-muted-foreground text-lg">/100</span>
            </div>
            <div className="flex-1">
              <Progress 
                value={analysisData.goalProgress.overallScore} 
                className="h-3"
              />
              <div className="grid grid-cols-3 gap-2 mt-2 text-sm">
                <div className="text-center">
                  <div className="font-medium">{t('pages.reviews.analysis.projects')}</div>
                  <div className={getScoreColor(analysisData.goalProgress.categoryScores.projects)}>
                    {analysisData.goalProgress.categoryScores.projects}%
                  </div>
                </div>
                <div className="text-center">
                  <div className="font-medium">{t('pages.reviews.analysis.habits')}</div>
                  <div className={getScoreColor(analysisData.goalProgress.categoryScores.habits)}>
                    {analysisData.goalProgress.categoryScores.habits}%
                  </div>
                </div>
                <div className="text-center">
                  <div className="font-medium">{t('pages.reviews.analysis.kpis')}</div>
                  <div className={getScoreColor(analysisData.goalProgress.categoryScores.kpis)}>
                    {analysisData.goalProgress.categoryScores.kpis}%
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="trends" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="trends">{t('pages.reviews.analysis.trends')}</TabsTrigger>
          <TabsTrigger value="predictions">{t('pages.reviews.analysis.predictions')}</TabsTrigger>
          <TabsTrigger value="recommendations">{t('pages.reviews.analysis.recommendations')}</TabsTrigger>
          <TabsTrigger value="progress">{t('pages.reviews.analysis.progress')}</TabsTrigger>
        </TabsList>

        {/* Trends Tab */}
        <TabsContent value="trends" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">{t('pages.reviews.analysis.projectCompletion')}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-2xl font-bold">
                      {analysisData.trends.projectCompletion.current}%
                    </div>
                    <div className={`text-sm flex items-center gap-1 ${getTrendColor(analysisData.trends.projectCompletion.trend)}`}>
                      {getTrendIcon(analysisData.trends.projectCompletion.trend)}
                      {analysisData.trends.projectCompletion.change > 0 ? '+' : ''}{analysisData.trends.projectCompletion.change}%
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">{t('pages.reviews.analysis.taskProductivity')}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-2xl font-bold">
                      {analysisData.trends.taskProductivity.current}%
                    </div>
                    <div className={`text-sm flex items-center gap-1 ${getTrendColor(analysisData.trends.taskProductivity.trend)}`}>
                      {getTrendIcon(analysisData.trends.taskProductivity.trend)}
                      {analysisData.trends.taskProductivity.change > 0 ? '+' : ''}{analysisData.trends.taskProductivity.change}%
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">{t('pages.reviews.analysis.habitConsistency')}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-2xl font-bold">
                      {analysisData.trends.habitConsistency.current}%
                    </div>
                    <div className={`text-sm flex items-center gap-1 ${getTrendColor(analysisData.trends.habitConsistency.trend)}`}>
                      {getTrendIcon(analysisData.trends.habitConsistency.trend)}
                      {analysisData.trends.habitConsistency.change > 0 ? '+' : ''}{analysisData.trends.habitConsistency.change}%
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Predictions Tab */}
        <TabsContent value="predictions" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">{t('pages.reviews.analysis.nextPeriodProjections')}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span>{t('pages.reviews.analysis.expectedProjects')}:</span>
                  <span className="font-medium">{analysisData.predictions.nextPeriodProjections.expectedProjects}</span>
                </div>
                <div className="flex justify-between">
                  <span>{t('pages.reviews.analysis.expectedTasks')}:</span>
                  <span className="font-medium">{analysisData.predictions.nextPeriodProjections.expectedTasks}</span>
                </div>
                <div className="flex justify-between">
                  <span>{t('pages.reviews.analysis.goalAchievementProbability')}:</span>
                  <span className={`font-medium ${getScoreColor(analysisData.predictions.goalAchievementProbability)}`}>
                    {analysisData.predictions.goalAchievementProbability}%
                  </span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">{t('pages.reviews.analysis.riskFactors')}</CardTitle>
              </CardHeader>
              <CardContent>
                {analysisData.predictions.nextPeriodProjections.riskFactors.length > 0 ? (
                  <div className="space-y-2">
                    {analysisData.predictions.nextPeriodProjections.riskFactors.map((risk, index) => (
                      <div key={index} className="flex items-center gap-2 text-sm">
                        <AlertTriangle className="h-4 w-4 text-yellow-500" />
                        {risk}
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="flex items-center gap-2 text-sm text-green-600">
                    <CheckCircle className="h-4 w-4" />
                    {t('pages.reviews.analysis.noRiskFactors')}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {analysisData.predictions.recommendedFocus.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">{t('pages.reviews.analysis.recommendedFocus')}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {analysisData.predictions.recommendedFocus.map((focus, index) => (
                    <Badge key={index} variant="secondary">
                      {focus}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Recommendations Tab */}
        <TabsContent value="recommendations" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Lightbulb className="h-5 w-5 text-yellow-500" />
                  {t('pages.reviews.analysis.immediateActions')}
                </CardTitle>
                <CardDescription>{t('pages.reviews.analysis.thisWeek')}</CardDescription>
              </CardHeader>
              <CardContent>
                {analysisData.recommendations.immediate.length > 0 ? (
                  <div className="space-y-2">
                    {analysisData.recommendations.immediate.map((rec, index) => (
                      <div key={index} className="text-sm p-2 bg-yellow-50 border-l-2 border-yellow-500 rounded">
                        {rec}
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-sm text-muted-foreground">{t('pages.reviews.analysis.noImmediateActions')}</p>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Target className="h-5 w-5 text-blue-500" />
                  {t('pages.reviews.analysis.shortTermGoals')}
                </CardTitle>
                <CardDescription>{t('pages.reviews.analysis.thisMonth')}</CardDescription>
              </CardHeader>
              <CardContent>
                {analysisData.recommendations.shortTerm.length > 0 ? (
                  <div className="space-y-2">
                    {analysisData.recommendations.shortTerm.map((rec, index) => (
                      <div key={index} className="text-sm p-2 bg-blue-50 border-l-2 border-blue-500 rounded">
                        {rec}
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-sm text-muted-foreground">{t('pages.reviews.analysis.noShortTermRecommendations')}</p>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <TrendingUp className="h-5 w-5 text-green-500" />
                  {t('pages.reviews.analysis.longTermStrategy')}
                </CardTitle>
                <CardDescription>{t('pages.reviews.analysis.nextQuarter')}</CardDescription>
              </CardHeader>
              <CardContent>
                {analysisData.recommendations.longTerm.length > 0 ? (
                  <div className="space-y-2">
                    {analysisData.recommendations.longTerm.map((rec, index) => (
                      <div key={index} className="text-sm p-2 bg-green-50 border-l-2 border-green-500 rounded">
                        {rec}
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-sm text-muted-foreground">{t('pages.reviews.analysis.noLongTermRecommendations')}</p>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Progress Tab */}
        <TabsContent value="progress" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <CheckCircle className="h-5 w-5 text-green-500" />
                  {t('pages.reviews.analysis.achievements')}
                </CardTitle>
              </CardHeader>
              <CardContent>
                {analysisData.goalProgress.achievements.length > 0 ? (
                  <div className="space-y-2">
                    {analysisData.goalProgress.achievements.map((achievement, index) => (
                      <div key={index} className="flex items-center gap-2 text-sm">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        {achievement}
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-sm text-muted-foreground">{t('pages.reviews.analysis.noAchievements')}</p>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5 text-red-500" />
                  {t('pages.reviews.analysis.areasForImprovement')}
                </CardTitle>
              </CardHeader>
              <CardContent>
                {analysisData.goalProgress.gaps.length > 0 ? (
                  <div className="space-y-2">
                    {analysisData.goalProgress.gaps.map((gap, index) => (
                      <div key={index} className="flex items-center gap-2 text-sm">
                        <AlertTriangle className="h-4 w-4 text-red-500" />
                        {gap}
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-sm text-muted-foreground">{t('pages.reviews.analysis.noGaps')}</p>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Category Breakdown */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">{t('pages.reviews.analysis.performanceBreakdown')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>{t('pages.reviews.analysis.projects')}</span>
                    <span className={getScoreColor(analysisData.goalProgress.categoryScores.projects)}>
                      {analysisData.goalProgress.categoryScores.projects}%
                    </span>
                  </div>
                  <Progress value={analysisData.goalProgress.categoryScores.projects} className="h-2" />
                </div>

                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>{t('pages.reviews.analysis.habits')}</span>
                    <span className={getScoreColor(analysisData.goalProgress.categoryScores.habits)}>
                      {analysisData.goalProgress.categoryScores.habits}%
                    </span>
                  </div>
                  <Progress value={analysisData.goalProgress.categoryScores.habits} className="h-2" />
                </div>

                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>{t('pages.reviews.analysis.kpis')}</span>
                    <span className={getScoreColor(analysisData.goalProgress.categoryScores.kpis)}>
                      {analysisData.goalProgress.categoryScores.kpis}%
                    </span>
                  </div>
                  <Progress value={analysisData.goalProgress.categoryScores.kpis} className="h-2" />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default ReviewAnalysis
