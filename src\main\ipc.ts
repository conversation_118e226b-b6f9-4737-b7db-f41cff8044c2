import { ipcMain, dialog, app, BrowserWindow } from 'electron'
import databaseService from './database'
import fileSystemService from './fileSystem'
import fileWatcherService from './fileWatcher'
import { registerGlobalShortcut, unregisterGlobalShortcut, unregisterAllGlobalShortcuts } from './index'
import { IPC_CHANNELS } from '../shared/ipcTypes'
import type {
  CreateProjectRequest,
  UpdateProjectRequest,
  CreateAreaRequest,
  CreateTaskRequest,
  UpdateTaskRequest,
  CreateResourceRequest,
  CreateProjectKPIRequest,
  UpdateProjectKPIRequest,
  CreateKPIRecordRequest,
  UpdateKPIRecordRequest,
  CreateAreaMetricRequest,
  UpdateAreaMetricRequest,
  CreateAreaMetricRecordRequest,
  UpdateAreaMetricRecordRequest,
  CreateDeliverableRequest,
  UpdateDeliverableRequest,
  LinkResourceToProjectRequest,
  CreateDocumentLinkRequest,
  UpdateDocumentLinkRequest,
  UpdateDocumentPathRequest,
  SearchLinksRequest,
  ReplaceDocumentLinksRequest,
  CreateChecklistRequest,
  UpdateChecklistRequest,
  CreateChecklistInstanceRequest,
  UpdateChecklistInstanceRequest,
  CreateReviewRequest,
  UpdateReviewRequest,
  GetReviewsRequest,
  CreateReviewTemplateRequest,
  UpdateReviewTemplateRequest,
  ReadFileRequest,
  WriteFileRequest,
  MoveFileRequest,
  CopyFileRequest
} from '../shared/ipcTypes'

class IpcHandler {
  private isInitialized = false

  /**
   * Initialize IPC handlers
   */
  initialize(): void {
    if (this.isInitialized) {
      return
    }

    this.setupDatabaseHandlers()
    this.setupFileSystemHandlers()
    this.setupAppHandlers()
    this.setupWindowHandlers()
    this.setupEventForwarding()

    this.isInitialized = true
    console.log('IPC handlers initialized')
  }

  /**
   * Setup database operation handlers
   */
  private setupDatabaseHandlers(): void {
    // Database initialization
    ipcMain.handle(IPC_CHANNELS.DB_INITIALIZE, async () => {
      try {
        return await databaseService.initialize()
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Database initialization failed'
        }
      }
    })

    // Test database connection
    ipcMain.handle(IPC_CHANNELS.DB_TEST_CONNECTION, async () => {
      try {
        return await databaseService.testConnection()
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Connection test failed'
        }
      }
    })

    // Project operations
    ipcMain.handle(IPC_CHANNELS.DB_CREATE_PROJECT, async (_, data: CreateProjectRequest) => {
      try {
        return await databaseService.createProject(data)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to create project'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_GET_PROJECTS, async () => {
      try {
        return await databaseService.getProjects()
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get projects'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_GET_PROJECT_BY_ID, async (_, id: string, includeArchived: boolean = false) => {
      try {
        return await databaseService.getProjectById(id, includeArchived)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get project by id'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_UPDATE_PROJECT, async (_, data: UpdateProjectRequest) => {
      try {
        const client = databaseService.getClient()
        const result = await client.project.update({
          where: { id: data.id },
          data: data.updates
        })
        return {
          success: true,
          data: result
        }
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to update project'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_DELETE_PROJECT, async (_, id: string) => {
      try {
        return await databaseService.deleteProject(id)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to delete project'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_ARCHIVE_PROJECT, async (_, id: string) => {
      try {
        return await databaseService.archiveProject(id)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to archive project'
        }
      }
    })

    // ProjectKPI operations
    ipcMain.handle(IPC_CHANNELS.DB_CREATE_PROJECT_KPI, async (_, data: CreateProjectKPIRequest) => {
      try {
        return await databaseService.createProjectKPI(data)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to create project KPI'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_GET_PROJECT_KPIS, async (_, projectId: string) => {
      try {
        return await databaseService.getProjectKPIs(projectId)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get project KPIs'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_UPDATE_PROJECT_KPI, async (_, data: UpdateProjectKPIRequest) => {
      try {
        return await databaseService.updateProjectKPI(data)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to update project KPI'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_DELETE_PROJECT_KPI, async (_, id: string) => {
      try {
        return await databaseService.deleteProjectKPI(id)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to delete project KPI'
        }
      }
    })

    // KPI Record operations
    ipcMain.handle(IPC_CHANNELS.DB_CREATE_KPI_RECORD, async (_, data: CreateKPIRecordRequest) => {
      try {
        return await databaseService.createKPIRecord(data)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to create KPI record'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_GET_KPI_RECORDS, async (_, kpiId: string, limit?: number) => {
      try {
        return await databaseService.getKPIRecords(kpiId, limit)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get KPI records'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_UPDATE_KPI_RECORD, async (_, data: UpdateKPIRecordRequest) => {
      try {
        return await databaseService.updateKPIRecord(data)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to update KPI record'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_DELETE_KPI_RECORD, async (_, id: string) => {
      try {
        return await databaseService.deleteKPIRecord(id)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to delete KPI record'
        }
      }
    })

    // Area Metric operations
    ipcMain.handle(IPC_CHANNELS.DB_CREATE_AREA_METRIC, async (_, data: CreateAreaMetricRequest) => {
      try {
        return await databaseService.createAreaMetric(data)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to create area metric'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_GET_AREA_METRICS, async (_, areaId: string) => {
      try {
        return await databaseService.getAreaMetrics(areaId)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get area metrics'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_UPDATE_AREA_METRIC, async (_, data: UpdateAreaMetricRequest) => {
      try {
        return await databaseService.updateAreaMetric(data)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to update area metric'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_DELETE_AREA_METRIC, async (_, id: string) => {
      try {
        return await databaseService.deleteAreaMetric(id)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to delete area metric'
        }
      }
    })

    // Area Metric Record operations
    ipcMain.handle(IPC_CHANNELS.DB_CREATE_AREA_METRIC_RECORD, async (_, data: CreateAreaMetricRecordRequest) => {
      try {
        return await databaseService.createAreaMetricRecord(data)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to create area metric record'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_GET_AREA_METRIC_RECORDS, async (_, metricId: string, limit?: number) => {
      try {
        return await databaseService.getAreaMetricRecords(metricId, limit)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get area metric records'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_UPDATE_AREA_METRIC_RECORD, async (_, data: UpdateAreaMetricRecordRequest) => {
      try {
        return await databaseService.updateAreaMetricRecord(data)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to update area metric record'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_DELETE_AREA_METRIC_RECORD, async (_, id: string) => {
      try {
        return await databaseService.deleteAreaMetricRecord(id)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to delete area metric record'
        }
      }
    })

    // Habit operations
    ipcMain.handle(IPC_CHANNELS.DB_GET_HABITS_BY_AREA, async (_, areaId: string) => {
      try {
        return await databaseService.getHabitsByArea(areaId)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get habits by area'
        }
      }
    })

    // Deliverable operations
    ipcMain.handle(IPC_CHANNELS.DB_CREATE_DELIVERABLE, async (_, data: CreateDeliverableRequest) => {
      try {
        return await databaseService.createDeliverable(data)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to create deliverable'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_GET_PROJECT_DELIVERABLES, async (_, projectId: string) => {
      try {
        return await databaseService.getProjectDeliverables(projectId)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get project deliverables'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_UPDATE_DELIVERABLE, async (_, data: UpdateDeliverableRequest) => {
      try {
        return await databaseService.updateDeliverable(data)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to update deliverable'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_DELETE_DELIVERABLE, async (_, id: string) => {
      try {
        return await databaseService.deleteDeliverable(id)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to delete deliverable'
        }
      }
    })

    // Habit operations
    ipcMain.handle(IPC_CHANNELS.DB_CREATE_HABIT, async (_, data: CreateHabitRequest) => {
      try {
        return await databaseService.createHabit(data)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to create habit'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_UPDATE_HABIT, async (_, data: UpdateHabitRequest) => {
      try {
        return await databaseService.updateHabit(data)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to update habit'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_DELETE_HABIT, async (_, id: string) => {
      try {
        return await databaseService.deleteHabit(id)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to delete habit'
        }
      }
    })

    // Habit record operations
    ipcMain.handle(IPC_CHANNELS.DB_CREATE_HABIT_RECORD, async (_, data: CreateHabitRecordRequest) => {
      try {
        return await databaseService.createHabitRecord(data)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to create habit record'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_UPDATE_HABIT_RECORD, async (_, data: UpdateHabitRecordRequest) => {
      try {
        return await databaseService.updateHabitRecord(data)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to update habit record'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_GET_HABIT_RECORDS, async (_, habitId: string) => {
      try {
        return await databaseService.getHabitRecords(habitId)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get habit records'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_DELETE_HABIT_RECORD, async (_, id: string) => {
      try {
        return await databaseService.deleteHabitRecord(id)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to delete habit record'
        }
      }
    })

    // {{ AURA-X: Add - 定期维护任务IPC处理器. Approval: 寸止(ID:1738157400). }}
    // Recurring task operations
    ipcMain.handle(IPC_CHANNELS.DB_CREATE_RECURRING_TASK, async (_, data: CreateRecurringTaskRequest) => {
      try {
        return await databaseService.createRecurringTask(data)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to create recurring task'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_GET_RECURRING_TASKS, async (_, areaId: string) => {
      try {
        return await databaseService.getRecurringTasks(areaId)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get recurring tasks'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_UPDATE_RECURRING_TASK, async (_, data: UpdateRecurringTaskRequest) => {
      try {
        return await databaseService.updateRecurringTask(data)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to update recurring task'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_DELETE_RECURRING_TASK, async (_, id: string) => {
      try {
        return await databaseService.deleteRecurringTask(id)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to delete recurring task'
        }
      }
    })

    // Resource linking operations
    ipcMain.handle(IPC_CHANNELS.DB_GET_PROJECT_RESOURCES, async (_, projectId: string) => {
      try {
        return await databaseService.getProjectResources(projectId)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get project resources'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_LINK_RESOURCE_TO_PROJECT, async (_, data: LinkResourceToProjectRequest) => {
      try {
        return await databaseService.linkResourceToProject(data)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to link resource to project'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_UNLINK_RESOURCE_FROM_PROJECT, async (_, resourceId: string, projectId: string) => {
      try {
        return await databaseService.unlinkResourceFromProject(resourceId, projectId)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to unlink resource from project'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_GET_AREA_RESOURCES, async (_, areaId: string) => {
      try {
        return await databaseService.getAreaResources(areaId)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get area resources'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_GET_RESOURCE_REFERENCES, async (_, resourcePath: string) => {
      try {
        return await databaseService.getResourceReferences(resourcePath)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get resource references'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_UNLINK_RESOURCE_FROM_AREA, async (_, resourceId: string, areaId: string) => {
      try {
        return await databaseService.unlinkResourceFromArea(resourceId, areaId)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to unlink resource from area'
        }
      }
    })

    // Area operations
    ipcMain.handle(IPC_CHANNELS.DB_CREATE_AREA, async (_, data: CreateAreaRequest) => {
      try {
        const client = databaseService.getClient()
        // {{ AURA-X: Modify - 支持完整的Area字段. Approval: 寸止(ID:1738157400). }}
        const result = await client.area.create({
          data: {
            name: data.name,
            description: data.description || null,
            color: data.color || null,
            icon: data.icon || null,
            standard: data.standard || null,
            status: data.status || 'Active',
            reviewFrequency: data.reviewFrequency || 'Weekly',
            archived: data.archived || false
          }
        })
        return {
          success: true,
          data: result
        }
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to create area'
        }
      }
    ipcMain.handle(IPC_CHANNELS.DB_UPDATE_AREA, async (_, data: UpdateAreaRequest) => {
      try {
        const client = databaseService.getClient()
        const result = await client.area.update({
          where: { id: data.id },
          data: data.updates
        })
        return {
          success: true,
          data: result
        }
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to update area'
        }
      }
    })

    })

    ipcMain.handle(IPC_CHANNELS.DB_GET_AREAS, async () => {
      try {
        return await databaseService.getAreas()
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get areas'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_GET_AREA_BY_ID, async (_, id: string, includeArchived: boolean = false) => {
      try {
        return await databaseService.getAreaById(id, includeArchived)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get area by id'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_DELETE_AREA, async (_, id: string) => {
      try {
        return await databaseService.deleteArea(id)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to delete area'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_ARCHIVE_AREA, async (_, id: string) => {
      try {
        return await databaseService.archiveArea(id)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to archive area'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_GET_ARCHIVED_PROJECTS, async () => {
      try {
        return await databaseService.getArchivedProjects()
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get archived projects'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_GET_ARCHIVED_AREAS, async () => {
      try {
        return await databaseService.getArchivedAreas()
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get archived areas'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_RESTORE_PROJECT, async (_, id: string) => {
      try {
        return await databaseService.restoreProject(id)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to restore project'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_RESTORE_AREA, async (_, id: string) => {
      try {
        return await databaseService.restoreArea(id)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to restore area'
        }
      }
    })

    // Task operations
    ipcMain.handle(IPC_CHANNELS.DB_CREATE_TASK, async (_, data: CreateTaskRequest) => {
      try {
        console.log('Creating task with data:', {
          content: data.content,
          projectId: data.projectId,
          areaId: data.areaId
        })

        const client = databaseService.getClient()

        // 验证外键约束 - 只有当ID存在且有效时才设置
        const taskData: any = {
          content: data.content,
          description: data.description,
          priority: data.priority,
          deadline: data.deadline,
          sourceText: data.sourceText,
          sourceContext: data.sourceContext
        }

        // 只有当projectId有效时才设置
        if (data.projectId && data.projectId !== 'none') {
          // 验证项目是否存在
          const project = await client.project.findUnique({
            where: { id: data.projectId }
          })
          if (project) {
            taskData.projectId = data.projectId
            console.log('Task assigned to project:', project.name)
          } else {
            console.warn('Project not found:', data.projectId)
            // 项目不存在时，任务将保持未分配状态
          }
        }

        // 只有当areaId有效时才设置
        if (data.areaId && data.areaId !== 'none') {
          // 验证领域是否存在
          const area = await client.area.findUnique({
            where: { id: data.areaId }
          })
          if (area) {
            taskData.areaId = data.areaId
            console.log('Task assigned to area:', area.name)
          } else {
            console.warn('Area not found:', data.areaId)
            // 领域不存在时，任务将保持未分配状态
          }
        }

        // 只有当parentId有效时才设置
        if (data.parentId) {
          // 验证父任务是否存在
          const parentTask = await client.task.findUnique({
            where: { id: data.parentId }
          })
          if (parentTask) {
            taskData.parentId = data.parentId
          }
        }

        // 只有当sourceResourceId有效时才设置
        if (data.sourceResourceId) {
          // 验证资源是否存在
          const resource = await client.resourceLink.findUnique({
            where: { id: data.sourceResourceId }
          })
          if (resource) {
            taskData.sourceResourceId = data.sourceResourceId
          }
        }

        const result = await client.task.create({
          data: taskData
        })

        console.log('Task created successfully:', {
          id: result.id,
          content: result.content,
          projectId: result.projectId,
          areaId: result.areaId
        })

        return {
          success: true,
          data: result
        }
      } catch (error) {
        console.error('Task creation error:', error)
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to create task'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_GET_TASKS, async (_, filters?: any) => {
      try {
        const client = databaseService.getClient()
        const where: any = {}

        if (filters?.projectId) {
          where.projectId = filters.projectId
        }
        if (filters?.areaId) {
          where.areaId = filters.areaId
        }
        if (filters?.completed !== undefined) {
          where.completed = filters.completed
        }

        const result = await client.task.findMany({
          where,
          orderBy: { createdAt: 'desc' },
          include: {
            tags: {
              include: {
                tag: true
              }
            }
          }
        })
        return {
          success: true,
          data: result
        }
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get tasks'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_UPDATE_TASK, async (_, data: UpdateTaskRequest) => {
      try {
        const client = databaseService.getClient()
        const result = await client.task.update({
          where: { id: data.id },
          data: data.updates
        })
        return {
          success: true,
          data: result
        }
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to update task'
        }
      }
    })

    // Resource operations
    ipcMain.handle(IPC_CHANNELS.DB_CREATE_RESOURCE, async (_, data: CreateResourceRequest) => {
      try {
        const client = databaseService.getClient()

        // Validate that the referenced entities exist
        if (data.projectId) {
          const project = await client.project.findUnique({
            where: { id: data.projectId }
          })
          if (!project) {
            throw new Error(`Project with ID ${data.projectId} does not exist`)
          }
        }

        if (data.areaId) {
          const area = await client.area.findUnique({
            where: { id: data.areaId }
          })
          if (!area) {
            throw new Error(`Area with ID ${data.areaId} does not exist`)
          }
        }

        const result = await client.resourceLink.create({
          data: {
            resourcePath: data.resourcePath,
            title: data.title,
            projectId: data.projectId || null,
            areaId: data.areaId || null
          }
        })
        return {
          success: true,
          data: result
        }
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to create resource'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_GET_RESOURCES, async (_, filters?: any) => {
      try {
        const client = databaseService.getClient()
        const where: any = {}

        if (filters?.projectId) {
          where.projectId = filters.projectId
        }
        if (filters?.areaId) {
          where.areaId = filters.areaId
        }

        const result = await client.resourceLink.findMany({
          where,
          orderBy: { id: 'desc' }
        })
        return {
          success: true,
          data: result
        }
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get resources'
        }
      }
    })

    // Document Link operations
    ipcMain.handle(
      IPC_CHANNELS.DB_CREATE_DOCUMENT_LINK,
      async (_, data: CreateDocumentLinkRequest) => {
        try {
          const linkService = databaseService.getDocumentLinkService()
          return await linkService.createLink(data)
        } catch (error) {
          return {
            success: false,
            error: error instanceof Error ? error.message : 'Failed to create document link'
          }
        }
      }
    )

    ipcMain.handle(IPC_CHANNELS.DB_GET_DOCUMENT_LINKS, async (_, docPath: string) => {
      try {
        const linkService = databaseService.getDocumentLinkService()
        return await linkService.getDocumentLinks(docPath)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get document links'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_GET_BACKLINKS, async (_, docPath: string) => {
      try {
        const linkService = databaseService.getDocumentLinkService()
        return await linkService.getBacklinks(docPath)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get backlinks'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_GET_OUTLINKS, async (_, docPath: string) => {
      try {
        const linkService = databaseService.getDocumentLinkService()
        return await linkService.getOutlinks(docPath)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get outlinks'
        }
      }
    })

    ipcMain.handle(
      IPC_CHANNELS.DB_UPDATE_DOCUMENT_LINK,
      async (_, data: UpdateDocumentLinkRequest) => {
        try {
          const linkService = databaseService.getDocumentLinkService()
          return await linkService.updateLink(data)
        } catch (error) {
          return {
            success: false,
            error: error instanceof Error ? error.message : 'Failed to update document link'
          }
        }
      }
    )

    ipcMain.handle(IPC_CHANNELS.DB_DELETE_DOCUMENT_LINK, async (_, id: string) => {
      try {
        const linkService = databaseService.getDocumentLinkService()
        return await linkService.deleteLink(id)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to delete document link'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_DELETE_DOCUMENT_LINKS, async (_, docPath: string) => {
      try {
        const linkService = databaseService.getDocumentLinkService()
        return await linkService.deleteDocumentLinks(docPath)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to delete document links'
        }
      }
    })

    ipcMain.handle(
      IPC_CHANNELS.DB_UPDATE_DOCUMENT_PATH,
      async (_, data: UpdateDocumentPathRequest) => {
        try {
          const linkService = databaseService.getDocumentLinkService()
          return await linkService.updateDocumentPath(data.oldPath, data.newPath, data.newTitle)
        } catch (error) {
          return {
            success: false,
            error: error instanceof Error ? error.message : 'Failed to update document path'
          }
        }
      }
    )

    ipcMain.handle(IPC_CHANNELS.DB_MARK_LINKS_INVALID, async (_, targetDocPath: string) => {
      try {
        const linkService = databaseService.getDocumentLinkService()
        return await linkService.markLinksAsInvalid(targetDocPath)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to mark links as invalid'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_MARK_LINKS_VALID, async (_, targetDocPath: string) => {
      try {
        const linkService = databaseService.getDocumentLinkService()
        return await linkService.markLinksAsValid(targetDocPath)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to mark links as valid'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_GET_LINK_STATISTICS, async (_, docPath: string) => {
      try {
        const linkService = databaseService.getDocumentLinkService()
        return await linkService.getLinkStatistics(docPath)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get link statistics'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_SEARCH_LINKS, async (_, data: SearchLinksRequest) => {
      try {
        const linkService = databaseService.getDocumentLinkService()
        return await linkService.searchLinks(data.query, data.filters)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to search links'
        }
      }
    })

    ipcMain.handle(
      IPC_CHANNELS.DB_REPLACE_DOCUMENT_LINKS,
      async (_, data: ReplaceDocumentLinksRequest) => {
        try {
          const linkService = databaseService.getDocumentLinkService()
          return await linkService.replaceDocumentLinks(data.sourceDocPath, data.links)
        } catch (error) {
          return {
            success: false,
            error: error instanceof Error ? error.message : 'Failed to replace document links'
          }
        }
      }
    )

    // Checklist operations
    ipcMain.handle(IPC_CHANNELS.DB_CREATE_CHECKLIST, async (_, data: CreateChecklistRequest) => {
      try {
        return await databaseService.createChecklist(data)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to create checklist'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_GET_CHECKLISTS, async (_, areaId?: string) => {
      try {
        return await databaseService.getChecklists(areaId)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get checklists'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_UPDATE_CHECKLIST, async (_, data: UpdateChecklistRequest) => {
      try {
        return await databaseService.updateChecklist(data.id, data.updates)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to update checklist'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_DELETE_CHECKLIST, async (_, id: string) => {
      try {
        return await databaseService.deleteChecklist(id)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to delete checklist'
        }
      }
    })

    // Checklist Instance operations
    ipcMain.handle(IPC_CHANNELS.DB_CREATE_CHECKLIST_INSTANCE, async (_, data: CreateChecklistInstanceRequest) => {
      try {
        return await databaseService.createChecklistInstance(data)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to create checklist instance'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_GET_CHECKLIST_INSTANCES, async (_, areaId?: string) => {
      try {
        return await databaseService.getChecklistInstances(areaId)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get checklist instances'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_UPDATE_CHECKLIST_INSTANCE, async (_, data: UpdateChecklistInstanceRequest) => {
      try {
        return await databaseService.updateChecklistInstance(data.id, data.updates)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to update checklist instance'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_DELETE_CHECKLIST_INSTANCE, async (_, id: string) => {
      try {
        return await databaseService.deleteChecklistInstance(id)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to delete checklist instance'
        }
      }
    })

    // Review operations
    ipcMain.handle(IPC_CHANNELS.DB_CREATE_REVIEW, async (_, data: CreateReviewRequest) => {
      try {
        return await databaseService.createReview(data)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to create review'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_GET_REVIEWS, async (_, filters?: GetReviewsRequest) => {
      try {
        return await databaseService.getReviews(filters)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get reviews'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_GET_REVIEW_BY_ID, async (_, id: string) => {
      try {
        return await databaseService.getReviewById(id)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get review'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_UPDATE_REVIEW, async (_, data: UpdateReviewRequest) => {
      try {
        return await databaseService.updateReview(data)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to update review'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_DELETE_REVIEW, async (_, id: string) => {
      try {
        return await databaseService.deleteReview(id)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to delete review'
        }
      }
    })

    // Review Template operations
    ipcMain.handle(IPC_CHANNELS.DB_CREATE_REVIEW_TEMPLATE, async (_, data: CreateReviewTemplateRequest) => {
      try {
        return await databaseService.createReviewTemplate(data)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to create review template'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_GET_REVIEW_TEMPLATES, async (_, type?: string) => {
      try {
        return await databaseService.getReviewTemplates(type)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get review templates'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_GET_REVIEW_TEMPLATE_BY_ID, async (_, id: string) => {
      try {
        return await databaseService.getReviewTemplateById(id)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get review template'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_UPDATE_REVIEW_TEMPLATE, async (_, data: UpdateReviewTemplateRequest) => {
      try {
        return await databaseService.updateReviewTemplate(data)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to update review template'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_DELETE_REVIEW_TEMPLATE, async (_, id: string) => {
      try {
        return await databaseService.deleteReviewTemplate(id)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to delete review template'
        }
      }
    })

    // Review Data Aggregation
    ipcMain.handle(IPC_CHANNELS.DB_GET_REVIEW_AGGREGATED_DATA, async (_, type: string, period: string) => {
      try {
        return await databaseService.getReviewAggregatedData(type, period)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get review aggregated data'
        }
      }
    })

    // Review Analysis
    ipcMain.handle(IPC_CHANNELS.DB_GET_REVIEW_ANALYSIS, async (_, type: string, period: string) => {
      try {
        return await databaseService.getReviewAnalysis(type, period)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get review analysis'
        }
      }
    })
  }

  /**
   * Setup file system operation handlers
   */
  private setupFileSystemHandlers(): void {
    // File system initialization
    ipcMain.handle(IPC_CHANNELS.FS_INITIALIZE, async () => {
      try {
        return await fileSystemService.initialize()
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'File system initialization failed'
        }
      }
    })

    // File system re-initialization with custom workspace
    ipcMain.handle(IPC_CHANNELS.FS_REINITIALIZE, async (_, workspaceDirectory: string) => {
      try {
        return await fileSystemService.initialize(workspaceDirectory)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'File system re-initialization failed'
        }
      }
    })

    // File operations
    ipcMain.handle(IPC_CHANNELS.FS_READ_FILE, async (_, data: ReadFileRequest) => {
      try {
        return await fileSystemService.readFile(data.path, { encoding: data.encoding })
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to read file'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.FS_WRITE_FILE, async (_, data: WriteFileRequest) => {
      try {
        return await fileSystemService.writeFile(data.path, data.content, {
          encoding: data.encoding,
          createDirs: data.createDirs,
          backup: data.backup
        })
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to write file'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.FS_DELETE_FILE, async (_, path: string) => {
      try {
        return await fileSystemService.deleteFile(path)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to delete file'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.FS_MOVE_FILE, async (_, data: MoveFileRequest) => {
      try {
        return await fileSystemService.moveFile(data.sourcePath, data.targetPath, {
          overwrite: data.overwrite,
          createDirs: data.createDirs
        })
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to move file'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.FS_COPY_FILE, async (_, data: CopyFileRequest) => {
      try {
        return await fileSystemService.copyFile(data.sourcePath, data.targetPath, {
          overwrite: data.overwrite,
          createDirs: data.createDirs
        })
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to copy file'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.FS_FILE_EXISTS, async (_, path: string) => {
      try {
        return await fileSystemService.fileExists(path)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to check file existence'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.FS_GET_FILE_INFO, async (_, path: string) => {
      try {
        return await fileSystemService.getFileInfo(path)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get file info'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.FS_LIST_DIRECTORY, async (_, path: string) => {
      try {
        return await fileSystemService.listDirectory(path)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to list directory'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.FS_CREATE_DIRECTORY, async (_, path: string) => {
      try {
        return await fileSystemService.createDirectory(path)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to create directory'
        }
      }
    })

    // File system delete directory
    ipcMain.handle(IPC_CHANNELS.FS_DELETE_DIRECTORY, async (_, path: string) => {
      try {
        return await fileSystemService.deleteDirectory(path)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to delete directory'
        }
      }
    })

    // File system rename
    ipcMain.handle(IPC_CHANNELS.FS_RENAME, async (_, oldPath: string, newPath: string) => {
      try {
        return await fileSystemService.rename(oldPath, newPath)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to rename file or directory'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.FS_WATCH_DIRECTORY, async (_, path: string) => {
      try {
        return await fileWatcherService.watchDirectory(path)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to watch directory'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.FS_UNWATCH_DIRECTORY, async (_, path: string) => {
      try {
        return await fileWatcherService.unwatchDirectory(path)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to unwatch directory'
        }
      }
    })
  }

  /**
   * Setup application operation handlers
   */
  private setupAppHandlers(): void {
    ipcMain.handle(IPC_CHANNELS.APP_GET_VERSION, async () => {
      return app.getVersion()
    })

    ipcMain.handle(IPC_CHANNELS.APP_GET_PATH, async (_, name: string) => {
      return app.getPath(name as any)
    })

    ipcMain.handle(IPC_CHANNELS.APP_SHOW_MESSAGE_BOX, async (_, options: any) => {
      const focusedWindow = BrowserWindow.getFocusedWindow()
      if (focusedWindow) {
        return await dialog.showMessageBox(focusedWindow, options)
      } else {
        return await dialog.showMessageBox(options)
      }
    })

    ipcMain.handle(IPC_CHANNELS.APP_SHOW_ERROR_BOX, async (_, title: string, content: string) => {
      dialog.showErrorBox(title, content)
    })

    ipcMain.handle(IPC_CHANNELS.APP_SHOW_OPEN_DIALOG, async (_, options: any) => {
      const focusedWindow = BrowserWindow.getFocusedWindow()
      if (focusedWindow) {
        return await dialog.showOpenDialog(focusedWindow, options)
      } else {
        return await dialog.showOpenDialog(options)
      }
    })

    ipcMain.handle(IPC_CHANNELS.APP_SHOW_SAVE_DIALOG, async (_, options: any) => {
      const focusedWindow = BrowserWindow.getFocusedWindow()
      if (focusedWindow) {
        return await dialog.showSaveDialog(focusedWindow, options)
      } else {
        return await dialog.showSaveDialog(options)
      }
    })
  }

  /**
   * Setup window operation handlers
   */
  private setupWindowHandlers(): void {
    ipcMain.handle(IPC_CHANNELS.WINDOW_MINIMIZE, async () => {
      const focusedWindow = BrowserWindow.getFocusedWindow()
      if (focusedWindow) {
        focusedWindow.minimize()
      }
    })

    ipcMain.handle(IPC_CHANNELS.WINDOW_MAXIMIZE, async () => {
      const focusedWindow = BrowserWindow.getFocusedWindow()
      if (focusedWindow) {
        if (focusedWindow.isMaximized()) {
          focusedWindow.unmaximize()
        } else {
          focusedWindow.maximize()
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.WINDOW_CLOSE, async () => {
      const focusedWindow = BrowserWindow.getFocusedWindow()
      if (focusedWindow) {
        focusedWindow.close()
      }
    })

    ipcMain.handle(IPC_CHANNELS.WINDOW_TOGGLE_DEVTOOLS, async () => {
      const focusedWindow = BrowserWindow.getFocusedWindow()
      if (focusedWindow) {
        focusedWindow.webContents.toggleDevTools()
      }
    })

    // Global shortcut handlers
    ipcMain.handle(IPC_CHANNELS.GLOBAL_SHORTCUT_REGISTER, async (_, accelerator: string, action: string) => {
      try {
        return registerGlobalShortcut(accelerator, action)
      } catch (error) {
        console.error('Error in global shortcut register handler:', error)
        return false
      }
    })

    ipcMain.handle(IPC_CHANNELS.GLOBAL_SHORTCUT_UNREGISTER, async (_, accelerator: string) => {
      try {
        unregisterGlobalShortcut(accelerator)
      } catch (error) {
        console.error('Error in global shortcut unregister handler:', error)
      }
    })

    ipcMain.handle(IPC_CHANNELS.GLOBAL_SHORTCUT_UNREGISTER_ALL, async () => {
      try {
        unregisterAllGlobalShortcuts()
      } catch (error) {
        console.error('Error in global shortcut unregister all handler:', error)
      }
    })

    // Settings operations
    ipcMain.handle(IPC_CHANNELS.SETTINGS_GET_USER_SETTINGS, async () => {
      try {
        const client = databaseService.getClient()
        // 从数据库获取用户设置，如果没有则返回默认值
        const users = await client.user.findMany()
        const userSettings = users.length > 0 ? users[0].settings : null

        return {
          success: true,
          data: userSettings
        }
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get user settings'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.SETTINGS_UPDATE_USER_SETTINGS, async (_, settings) => {
      try {
        const client = databaseService.getClient()
        // 更新或创建用户设置
        const existingUser = await client.user.findFirst()

        if (existingUser) {
          await client.user.update({
            where: { id: existingUser.id },
            data: { settings }
          })
        } else {
          await client.user.create({
            data: {
              username: settings.username || 'User',
              password: 'default', // 临时密码
              settings
            }
          })
        }

        return {
          success: true,
          data: settings
        }
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to update user settings'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.SETTINGS_RESET_USER_SETTINGS, async () => {
      try {
        const client = databaseService.getClient()
        const existingUser = await client.user.findFirst()

        if (existingUser) {
          await client.user.update({
            where: { id: existingUser.id },
            data: { settings: null }
          })
        }

        return {
          success: true,
          data: null
        }
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to reset user settings'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.SETTINGS_GET_DATABASE_INFO, async () => {
      try {
        const client = databaseService.getClient()

        // 获取各表的记录数
        const [projectCount, areaCount, taskCount, resourceCount] = await Promise.all([
          client.project.count(),
          client.area.count(),
          client.task.count(),
          client.resourceLink.count()
        ])

        return {
          success: true,
          data: {
            projectCount,
            areaCount,
            taskCount,
            resourceCount,
            totalItems: projectCount + areaCount + taskCount + resourceCount
          }
        }
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get database info'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.SETTINGS_EXPORT_DATA, async () => {
      try {
        const client = databaseService.getClient()

        // 导出所有数据
        const [projects, areas, tasks, resources, users] = await Promise.all([
          client.project.findMany({
            include: {
              tasks: true,
              resourceLinks: true
            }
          }),
          client.area.findMany({
            include: {
              projects: true,
              resourceLinks: true
            }
          }),
          client.task.findMany(),
          client.resourceLink.findMany(),
          client.user.findMany()
        ])

        const exportData = {
          version: '1.0.0',
          exportDate: new Date().toISOString(),
          data: {
            projects,
            areas,
            tasks,
            resources,
            users: users.map(user => ({
              ...user,
              password: undefined // 不导出密码
            }))
          }
        }

        return {
          success: true,
          data: exportData
        }
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to export data'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.SETTINGS_IMPORT_DATA, async (_, data) => {
      try {
        // TODO: 实现完整的数据导入功能
        return {
          success: false,
          error: 'Import data feature is not implemented yet'
        }
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to import data'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.SETTINGS_CREATE_BACKUP, async () => {
      try {
        const fs = require('fs').promises
        const path = require('path')
        const { app } = require('electron')

        // 获取数据库文件路径
        const userDataPath = app.getPath('userData')
        const dbPath = path.join(userDataPath, 'database.db')

        // 创建备份目录
        const backupDir = path.join(userDataPath, 'backups')
        await fs.mkdir(backupDir, { recursive: true })

        // 生成备份文件名
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
        const backupPath = path.join(backupDir, `backup-${timestamp}.db`)

        // 复制数据库文件
        await fs.copyFile(dbPath, backupPath)

        // 获取备份文件信息
        const stats = await fs.stat(backupPath)

        return {
          success: true,
          data: {
            backupPath,
            size: stats.size,
            createdAt: new Date().toISOString()
          }
        }
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to create backup'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.SETTINGS_SELECT_RESOURCE_PATH, async () => {
      try {
        const { dialog } = require('electron')

        const result = await dialog.showOpenDialog({
          properties: ['openDirectory'],
          title: '选择资源库路径'
        })

        if (result.canceled || result.filePaths.length === 0) {
          return {
            success: false,
            error: 'User cancelled directory selection'
          }
        }

        return {
          success: true,
          data: result.filePaths[0]
        }
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to select resource path'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.SETTINGS_UPDATE_RESOURCE_PATH, async (_, path: string) => {
      try {
        const client = databaseService.getClient()
        const existingUser = await client.user.findFirst()

        if (existingUser) {
          const currentSettings = existingUser.settings || {}
          await client.user.update({
            where: { id: existingUser.id },
            data: {
              settings: {
                ...currentSettings,
                resourcePath: path
              }
            }
          })
        } else {
          await client.user.create({
            data: {
              username: 'User',
              password: 'default',
              settings: {
                resourcePath: path
              }
            }
          })
        }

        return {
          success: true,
          data: { resourcePath: path }
        }
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to update resource path'
        }
      }
    })
  }

  /**
   * Setup event forwarding from services to renderer
   */
  private setupEventForwarding(): void {
    // Forward file system events to all renderer processes
    fileWatcherService.on('file-system-event', (event) => {
      BrowserWindow.getAllWindows().forEach((window) => {
        window.webContents.send(IPC_CHANNELS.FS_EVENT, event)
      })
    })

    fileWatcherService.on('file-created', (event) => {
      BrowserWindow.getAllWindows().forEach((window) => {
        window.webContents.send(IPC_CHANNELS.FS_FILE_CREATED, event)
      })
    })

    fileWatcherService.on('file-changed', (event) => {
      BrowserWindow.getAllWindows().forEach((window) => {
        window.webContents.send(IPC_CHANNELS.FS_FILE_CHANGED, event)
      })
    })

    fileWatcherService.on('file-deleted', (event) => {
      BrowserWindow.getAllWindows().forEach((window) => {
        window.webContents.send(IPC_CHANNELS.FS_FILE_DELETED, event)
      })
    })
  }

  /**
   * Remove all IPC handlers
   */
  cleanup(): void {
    if (!this.isInitialized) {
      return
    }

    // Remove all handlers
    Object.values(IPC_CHANNELS).forEach((channel) => {
      ipcMain.removeAllListeners(channel)
    })

    this.isInitialized = false
    console.log('IPC handlers cleaned up')
  }
}

// Export singleton instance
export const ipcHandler = new IpcHandler()
export default ipcHandler
