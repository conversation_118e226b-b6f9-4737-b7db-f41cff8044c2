import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '../ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { Input } from '../ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select'
import { Calendar, Clock, Edit, Trash2, Plus, Search, Filter, Download, GitCompare, BarChart3 } from 'lucide-react'
import { useLanguage } from '../../contexts/LanguageContext'
import { databaseApi } from '../../lib/api'
import type { Review } from '../../../../shared/types'
import type { GetReviewsRequest } from '../../../../shared/ipcTypes'
import ReviewComparison from './ReviewComparison'

interface ReviewListProps {
  onCreateReview?: () => void
  onEditReview?: (review: Review) => void
  onDeleteReview?: (reviewId: string) => void
}

export function ReviewList({ onCreateReview, onEditReview, onDeleteReview }: ReviewListProps) {
  const { t } = useLanguage()
  const [reviews, setReviews] = useState<Review[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [typeFilter, setTypeFilter] = useState<string>('all')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [selectedReviews, setSelectedReviews] = useState<string[]>([])
  const [showComparison, setShowComparison] = useState(false)

  useEffect(() => {
    loadReviews()
  }, [typeFilter, statusFilter])

  const loadReviews = async () => {
    setLoading(true)
    try {
      const filters: GetReviewsRequest = {}
      if (typeFilter !== 'all') filters.type = typeFilter
      if (statusFilter !== 'all') filters.status = statusFilter

      const result = await databaseApi.getReviews(filters)
      if (result.success) {
        setReviews(result.data || [])
      } else {
        console.error('Failed to load reviews:', result.error)
      }
    } catch (error) {
      console.error('Error loading reviews:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteReview = async (reviewId: string) => {
    if (window.confirm(t('pages.reviews.list.deleteConfirm'))) {
      try {
        const result = await databaseApi.deleteReview(reviewId)
        if (result.success) {
          setReviews(prev => prev.filter(r => r.id !== reviewId))
          onDeleteReview?.(reviewId)
        } else {
          console.error('Failed to delete review:', result.error)
        }
      } catch (error) {
        console.error('Error deleting review:', error)
      }
    }
  }

  const filteredReviews = reviews.filter(review => {
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      return (
        review.title?.toLowerCase().includes(query) ||
        review.period.toLowerCase().includes(query) ||
        review.summary?.toLowerCase().includes(query)
      )
    }
    return true
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'default'
      case 'draft':
        return 'secondary'
      case 'archived':
        return 'outline'
      default:
        return 'secondary'
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'daily':
        return '📅'
      case 'weekly':
        return '📊'
      case 'monthly':
        return '📈'
      case 'quarterly':
        return '📋'
      case 'yearly':
        return '🎯'
      default:
        return '📝'
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }

  const toggleReviewSelection = (reviewId: string) => {
    setSelectedReviews(prev =>
      prev.includes(reviewId)
        ? prev.filter(id => id !== reviewId)
        : [...prev, reviewId]
    )
  }

  const exportReview = (review: Review) => {
    const exportData = {
      title: review.title || `${review.type} Review - ${review.period}`,
      type: review.type,
      period: review.period,
      content: review.content,
      summary: review.summary,
      insights: review.insights,
      actionItems: review.actionItems,
      status: review.status,
      createdAt: review.createdAt,
      completedAt: review.completedAt,
      exportedAt: new Date().toISOString()
    }

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `review_${review.period}_${review.type}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const exportSelectedReviews = () => {
    const selectedReviewData = reviews.filter(r => selectedReviews.includes(r.id))
    const exportData = {
      reviews: selectedReviewData,
      exportedAt: new Date().toISOString(),
      totalCount: selectedReviewData.length
    }

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `reviews_export_${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const compareReviews = () => {
    if (selectedReviews.length >= 2) {
      setShowComparison(true)
    }
  }

  const clearSelection = () => {
    setSelectedReviews([])
  }

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader>
              <div className="h-4 bg-muted rounded w-1/3"></div>
              <div className="h-3 bg-muted rounded w-1/2"></div>
            </CardHeader>
            <CardContent>
              <div className="h-3 bg-muted rounded w-full mb-2"></div>
              <div className="h-3 bg-muted rounded w-2/3"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header with Actions */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">{t('pages.reviews.list.title')}</h2>
          <p className="text-muted-foreground">
            {t('pages.reviews.list.description')}
            {selectedReviews.length > 0 && (
              <span className="ml-2 text-primary">
                ({t('pages.reviews.list.selected', { count: selectedReviews.length })})
              </span>
            )}
          </p>
        </div>
        <div className="flex items-center gap-2">
          {selectedReviews.length > 0 && (
            <>
              <Button variant="outline" size="sm" onClick={exportSelectedReviews}>
                <Download className="h-4 w-4 mr-2" />
                {t('pages.reviews.list.exportSelected', { count: selectedReviews.length })}
              </Button>
              {selectedReviews.length >= 2 && (
                <Button variant="outline" size="sm" onClick={compareReviews}>
                  <GitCompare className="h-4 w-4 mr-2" />
                  {t('pages.reviews.list.compare')}
                </Button>
              )}
              <Button variant="outline" size="sm" onClick={clearSelection}>
                {t('pages.reviews.list.clear')}
              </Button>
            </>
          )}
          <Button onClick={onCreateReview}>
            <Plus className="h-4 w-4 mr-2" />
            {t('pages.reviews.list.newReview')}
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder={t('pages.reviews.list.search')}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t('pages.reviews.list.all')}</SelectItem>
                  <SelectItem value="daily">{t('pages.reviews.editor.types.daily')}</SelectItem>
                  <SelectItem value="weekly">{t('pages.reviews.editor.types.weekly')}</SelectItem>
                  <SelectItem value="monthly">{t('pages.reviews.editor.types.monthly')}</SelectItem>
                  <SelectItem value="quarterly">{t('pages.reviews.editor.types.quarterly')}</SelectItem>
                  <SelectItem value="yearly">{t('pages.reviews.editor.types.yearly')}</SelectItem>
                </SelectContent>
              </Select>
              
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t('pages.reviews.list.all')}</SelectItem>
                  <SelectItem value="draft">{t('pages.reviews.editor.status.draft')}</SelectItem>
                  <SelectItem value="completed">{t('pages.reviews.editor.status.completed')}</SelectItem>
                  <SelectItem value="archived">{t('pages.reviews.editor.status.archived')}</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Reviews List */}
      <div className="space-y-4">
        {filteredReviews.length === 0 ? (
          <Card>
            <CardContent className="pt-6">
              <div className="text-center py-8">
                <Calendar className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">{t('pages.reviews.list.noReviews')}</h3>
                <p className="text-muted-foreground mb-4">
                  {searchQuery || typeFilter !== 'all' || statusFilter !== 'all'
                    ? t('pages.reviews.list.tryAdjustFilters')
                    : t('pages.reviews.list.createFirst')}
                </p>
                {!searchQuery && typeFilter === 'all' && statusFilter === 'all' && (
                  <Button onClick={onCreateReview}>
                    <Plus className="h-4 w-4 mr-2" />
                    {t('pages.reviews.list.createFirst')}
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        ) : (
          filteredReviews.map((review) => (
            <Card key={review.id} className={`hover:shadow-md transition-shadow ${selectedReviews.includes(review.id) ? 'ring-2 ring-primary' : ''}`}>
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-3">
                    <input
                      type="checkbox"
                      checked={selectedReviews.includes(review.id)}
                      onChange={() => toggleReviewSelection(review.id)}
                      className="h-4 w-4"
                    />
                    <span className="text-2xl">{getTypeIcon(review.type)}</span>
                    <div>
                      <CardTitle className="text-lg">
                        {review.title || `${t(`pages.reviews.editor.types.${review.type}`)} - ${review.period}`}
                      </CardTitle>
                      <CardDescription className="flex items-center gap-2 mt-1">
                        <Calendar className="h-3 w-3" />
                        {review.period}
                        <span className="text-muted-foreground">•</span>
                        <Clock className="h-3 w-3" />
                        {formatDate(review.createdAt)}
                      </CardDescription>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant={getStatusColor(review.status)}>
                      {t(`pages.reviews.editor.status.${review.status}`)}
                    </Badge>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => exportReview(review)}
                      title={t('pages.reviews.list.tooltip.export')}
                    >
                      <Download className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onEditReview?.(review)}
                      title={t('pages.reviews.list.tooltip.edit')}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteReview(review.id)}
                      title={t('pages.reviews.list.tooltip.delete')}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              {review.summary && (
                <CardContent>
                  <p className="text-muted-foreground line-clamp-2">
                    {review.summary}
                  </p>
                </CardContent>
              )}
            </Card>
          ))
        )}
      </div>

      {/* Review Comparison Modal */}
      {showComparison && selectedReviews.length >= 2 && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-6xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <ReviewComparison
                reviewIds={selectedReviews}
                onClose={() => setShowComparison(false)}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default ReviewList
