import { create } from 'zustand'

interface UserInfo {
  id: string
  username: string
  email: string
  avatar?: string
  isOnline: boolean
  theme: 'light' | 'dark' | 'system'
}

interface UserState {
  user: UserInfo | null
  setUser: (user: UserInfo) => void
  updateUser: (updates: Partial<UserInfo>) => void
  toggleTheme: () => void
  logout: () => void
}

export const useUserStore = create<UserState>((set, get) => ({
  user: {
    id: '1',
    username: '泡泡',
    email: '<EMAIL>',
    isOnline: true,
    theme: 'system'
  },
  
  setUser: (user: UserInfo) => set({ user }),
  
  updateUser: (updates: Partial<UserInfo>) => set((state) => ({
    user: state.user ? { ...state.user, ...updates } : null
  })),
  
  toggleTheme: () => {
    const currentUser = get().user
    if (!currentUser) return
    
    const themes: Array<'light' | 'dark' | 'system'> = ['light', 'dark', 'system']
    const currentIndex = themes.indexOf(currentUser.theme)
    const nextTheme = themes[(currentIndex + 1) % themes.length]
    
    get().updateUser({ theme: nextTheme })
    
    // 应用主题到文档
    const root = document.documentElement
    if (nextTheme === 'dark') {
      root.classList.add('dark')
    } else if (nextTheme === 'light') {
      root.classList.remove('dark')
    } else {
      // system theme
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
      if (prefersDark) {
        root.classList.add('dark')
      } else {
        root.classList.remove('dark')
      }
    }
  },
  
  logout: () => {
    set({ user: null })
    // 这里可以添加清理本地存储、重定向到登录页等逻辑
    console.log('用户已退出登录')
  }
}))
