import { useState, useMemo } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { Checkbox } from '../ui/checkbox'
import { cn } from '../../lib/utils'
import { useTaskStore } from '../../store/taskStore'
import { useProjectStore } from '../../store/projectStore'
import { useLanguage } from '../../contexts/LanguageContext'
import { formatDate } from '../../utils/i18n'

interface TodayTasksProps {
  className?: string
}

export function TodayTasks({ className }: TodayTasksProps) {
  const { tasks, completeTask, updateTask } = useTaskStore()
  const { projects } = useProjectStore()
  const [showCompleted, setShowCompleted] = useState(false)
  const { t, language } = useLanguage()

  // Get today's tasks (due today or overdue)
  const todayTasks = useMemo(() => {
    const today = new Date()
    today.setHours(0, 0, 0, 0)

    return tasks
      .filter((task) => {
        if (task.completed && !showCompleted) return false

        if (!task.deadline) return false

        const deadline = new Date(task.deadline)
        deadline.setHours(0, 0, 0, 0)

        // Include tasks due today or overdue
        return deadline <= today
      })
      .sort((a, b) => {
        // Sort by: incomplete first, then by deadline (overdue first)
        if (a.completed !== b.completed) {
          return a.completed ? 1 : -1
        }

        const aDeadline = new Date(a.deadline!).getTime()
        const bDeadline = new Date(b.deadline!).getTime()
        return aDeadline - bDeadline
      })
  }, [tasks, showCompleted])

  const overdueTasks = todayTasks.filter((task) => {
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    const deadline = new Date(task.deadline!)
    deadline.setHours(0, 0, 0, 0)
    return deadline < today && !task.completed
  })

  const completedToday = todayTasks.filter((task) => task.completed).length
  const totalToday = todayTasks.filter((task) => !task.completed).length

  const handleTaskToggle = (taskId: string, completed: boolean) => {
    if (completed) {
      completeTask(taskId)
    } else {
      updateTask(taskId, { completed: false })
    }
  }

  const getProjectName = (projectId?: string | null) => {
    if (!projectId) return null
    const project = projects.find((p) => p.id === projectId)
    return project?.name
  }

  const getPriorityColor = (priority?: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'low':
        return 'bg-green-100 text-green-800 border-green-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getPriorityLabel = (priority?: string) => {
    switch (priority) {
      case 'high':
        return t('enums.priority.high')
      case 'medium':
        return t('enums.priority.medium')
      case 'low':
        return t('enums.priority.low')
      default:
        return priority
    }
  }

  const isOverdue = (deadline: Date) => {
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    const taskDeadline = new Date(deadline)
    taskDeadline.setHours(0, 0, 0, 0)
    return taskDeadline < today
  }

  return (
    <div className={cn('w-full', className)}>
      {overdueTasks.length > 0 && (
        <div className="pb-3">
          <Badge variant="destructive" className="text-xs">
            {overdueTasks.length} 已逾期
          </Badge>
        </div>
      )}

      <div>
        {todayTasks.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <div className="text-4xl mb-2">🎉</div>
            <p className="text-sm">今天没有任务</p>
            <p className="text-xs mt-1">享受自由时光吧！</p>
          </div>
        ) : (
          <div className="space-y-2">
            {todayTasks.map((task) => (
              <div
                key={task.id}
                className={cn(
                  'flex items-start gap-3 p-3 rounded-lg border transition-all',
                  task.completed ? 'bg-muted/50 opacity-60' : 'bg-background hover:bg-accent/50',
                  isOverdue(task.deadline!) && !task.completed && 'border-red-200 bg-red-50/50'
                )}
              >
                <Checkbox
                  checked={task.completed}
                  onCheckedChange={(checked) => handleTaskToggle(task.id, checked as boolean)}
                  className="mt-0.5"
                />

                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between gap-2">
                    <div className="flex-1">
                      <p
                        className={cn(
                          'text-sm font-medium',
                          task.completed && 'line-through text-muted-foreground'
                        )}
                      >
                        {task.content}
                      </p>
                      {task.description && (
                        <p
                          className={cn(
                            'text-xs text-muted-foreground mt-1',
                            task.completed && 'line-through'
                          )}
                        >
                          {task.description}
                        </p>
                      )}
                    </div>

                    <div className="flex items-center gap-1 flex-shrink-0">
                      {task.priority && (
                        <Badge
                          variant="outline"
                          className={cn('text-xs', getPriorityColor(task.priority))}
                        >
                          {getPriorityLabel(task.priority)}
                        </Badge>
                      )}
                      {isOverdue(task.deadline!) && !task.completed && (
                        <Badge variant="destructive" className="text-xs">
                          {t('time.overdue')}
                        </Badge>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center gap-2 mt-2 text-xs text-muted-foreground">
                    {getProjectName(task.projectId) && (
                      <span className="flex items-center gap-1">
                        <div className="w-2 h-2 rounded-full bg-project"></div>
                        {getProjectName(task.projectId)}
                      </span>
                    )}
                    <span>
                      {t('time.due')}:{' '}
                      {formatDate(new Date(task.deadline!), language, { dateStyle: 'short' })}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

export default TodayTasks
