import React, { useEffect, useRef } from 'react'
import { Crepe } from '@milkdown/crepe'
import { Milkdown, MilkdownProvider, useEditor } from '@milkdown/react'
import { replaceAll } from '@milkdown/kit/utils'
import '@milkdown/crepe/theme/common/style.css'
import '@milkdown/crepe/theme/frame.css'
// WikiLink 插件样式
import '../../plugins/wikilink/styles.css'
import {
  wikiLinkConfigCtx,
  wikiLinkNode,
  wikiLinkRemarkPlugin,
  smartInputRules,
  wikiLinkPreviewPlugin,
  wikiLinkViewPlugin,
  type WikiLinkConfig
} from '../../plugins/wikilink'
import { cleanContent } from '../../plugins/wikilink/smartInput'
import { escapeHandlerPlugin } from '../../plugins/wikilink/escapeHandler'
import { extractWikiLinkReferences } from '../../plugins/wikilink/utils'
import { bidirectionalLinkService } from '../../services/bidirectionalLinkService'
import { useUserSettingsStore } from '../../store/userSettingsStore'

interface MarkdownEditorProps {
  /** 初始内容 */
  initialValue?: string
  /** 内容变化回调 */
  onChange?: (markdown: string) => void
  /** 是否只读 */
  readonly?: boolean
  /** 编辑器高度 */
  height?: string
  /** 自定义类名 */
  className?: string
  /** 占位符文本 */
  placeholder?: string
  /** WikiLink 配置 */
  wikiLinkConfig?: Partial<WikiLinkConfig>
  /** 页面点击处理函数 */
  onPageClick?: (pageName: string) => void
  /** 链接创建回调 */
  onLinkCreate?: (source: string, target: string) => void
  /** 当前页面名称（用于双向链接） */
  currentPageName?: string
  /** 强制主题（覆盖用户设置） */
  theme?: 'classic' | 'dark'
}

// 内部编辑器组件 - 使用官方React集成
const CrepeEditorCore: React.FC<{
  initialValue: string
  onChange?: (markdown: string) => void
  readonly: boolean
  placeholder?: string
  wikiLinkConfig?: Partial<WikiLinkConfig>
  onPageClick?: (pageName: string) => void
  onLinkCreate?: (source: string, target: string) => void
  currentPageName?: string
  theme?: 'classic' | 'dark'
}> = ({
  initialValue,
  onChange,
  readonly,
  placeholder = '开始编写...',
  wikiLinkConfig,
  onPageClick,
  onLinkCreate,
  currentPageName,
  theme
}) => {
  // 防重复更新的引用
  const lastContentRef = useRef<string>('')

  // 防止程序化更新触发双向链接更新的标记
  const isUpdatingProgrammaticallyRef = useRef<boolean>(false)
  const programmaticUpdateTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // 当前页面名称的引用，确保实时同步
  const currentPageNameRef = useRef<string>(currentPageName || '')

  // 获取用户设置
  const { settings } = useUserSettingsStore()

  // 自动保存相关状态
  const autoSaveTimerRef = useRef<NodeJS.Timeout | null>(null)
  const lastSaveContentRef = useRef<string>('')

  // 同步currentPageName引用
  useEffect(() => {
    currentPageNameRef.current = currentPageName || ''
    console.log('📄 currentPageName 已更新:', currentPageNameRef.current)
  }, [currentPageName])

  // 当文件内容加载完成后，智能更新WikiLink
  useEffect(() => {
    if (initialValue && currentPageName) {
      // 检查是否包含WikiLink，如果有则立即更新
      const hasWikiLink = /\[\[([^\]]+)\]\]/.test(initialValue)
      if (hasWikiLink) {
        console.log('🔄 检测到WikiLink，立即更新双向链接')
        // 使用较短的延迟，避免与编辑器初始化冲突
        const timer = setTimeout(() => {
          updateBidirectionalLinks(initialValue)
        }, 100) // 100ms延迟

        return () => clearTimeout(timer)
      }
    }
  }, [initialValue, currentPageName]) // 当文件内容或页面名称变化时触发

  // 更新双向链接的函数
  const updateBidirectionalLinks = async (markdown: string) => {
    const pageName = currentPageNameRef.current
    if (!pageName) {
      console.log('❌ 无法更新双向链接：currentPageName 为空')
      return
    }

    try {
      console.log('🔍 开始解析双向链接，内容长度:', markdown.length)
      console.log('📄 当前页面名称:', pageName)
      console.log('📄 内容预览:', markdown.substring(0, 200))

      // 检查内容中的特殊字符
      const hasWikiLinkPattern = /\[\[.*?\]\]/g.test(markdown)
      const hasEscapedPattern = /\\\[\\\[.*?\\\]\\\]/g.test(markdown)
      console.log('🔍 检查模式:', { hasWikiLinkPattern, hasEscapedPattern })

      // 显示原始字符码
      const chars = markdown.substring(0, 50).split('').map((char, i) => `${i}:${char}(${char.charCodeAt(0)})`).join(' ')
      // console.log('🔤 字符码:', chars)

      // 先处理转义的内容
      const unescapedMarkdown = markdown.replace(/\\\[/g, '[').replace(/\\\]/g, ']')
      console.log('🔧 处理转义后的内容:', unescapedMarkdown.substring(0, 100))

      // 提取WikiLink引用
      const references = extractWikiLinkReferences(unescapedMarkdown)
      console.log('🔗 找到的WikiLink引用:', references)

      // 如果还是没找到，尝试手动匹配
      if (references.length === 0) {
        const manualMatches = unescapedMarkdown.match(/\[\[([^\]]+)\]\]/g)
        console.log('🔍 手动匹配结果:', manualMatches)
      }

      if (references.length === 0) {
        console.log('⚠️ 未找到任何WikiLink引用')
        // 只清空当前文档作为源文档的链接，不影响其他文档指向当前文档的链接
        await bidirectionalLinkService.replaceDocumentLinks(pageName, [])
        console.log('🧹 已清空当前文档的出链（不影响反向链接）')
        return
      }

      // 转换为创建链接请求格式
      const linkRequests = references.map((ref, index) => {
        // 使用unescapedMarkdown的位置信息
        const lines = unescapedMarkdown.substring(0, ref.startIndex).split('\n')
        const lineNumber = lines.length
        const columnNumber = lines[lines.length - 1].length + 1

        // 获取上下文（使用unescapedMarkdown）
        const contextBefore = unescapedMarkdown.substring(Math.max(0, ref.startIndex - 50), ref.startIndex)
        const contextAfter = unescapedMarkdown.substring(ref.endIndex, Math.min(unescapedMarkdown.length, ref.endIndex + 50))

        // 确保路径格式一致 - 都以"/"开头
        const sourceDocPath = pageName.startsWith('/') ? pageName : `/${pageName}`
        const targetDocPath = `/${ref.target}.md`

        console.log(`🔧 路径构建 - ref.target: "${ref.target}", targetDocPath: "${targetDocPath}"`)

        const linkRequest = {
          sourceDocPath,
          sourceDocTitle: pageName.split('/').pop()?.replace('.md', ''),
          targetDocPath,
          targetDocTitle: ref.target,
          linkText: ref.target,
          displayText: ref.display,
          linkType: 'wikilink',
          startPosition: ref.startIndex,
          endPosition: ref.endIndex,
          lineNumber,
          columnNumber,
          contextBefore,
          contextAfter,
          linkStrength: 1.0
        }

        console.log(`📝 创建链接请求 ${index + 1}:`, linkRequest)
        console.log(`🔍 路径格式检查 - 源文档: ${sourceDocPath}, 目标文档: ${targetDocPath}`)
        return linkRequest
      })

      // 批量替换文档的链接
      console.log('💾 开始保存链接到数据库...')
      await bidirectionalLinkService.replaceDocumentLinks(pageName, linkRequests)

      console.log(`✅ 更新了 ${linkRequests.length} 个双向链接`)

      // 清理UnifiedReferenceService的缓存，确保引用统计能及时更新
      const { unifiedReferenceService } = await import('../../services/unifiedReferenceService')
      unifiedReferenceService.clearCache(pageName)
      console.log('🗑️ 已清理UnifiedReferenceService缓存')
    } catch (error) {
      console.error('❌ 更新双向链接失败:', error)
    }
  }

  // 使用官方的useEditor钩子
  const { get } = useEditor((root) => {
    // 根据用户设置配置编辑器功能 - 恢复正常功能
    const features: Record<string, any> = {
      [Crepe.Feature.Toolbar]: !settings.focusMode, // 专注模式下隐藏工具栏
      [Crepe.Feature.BlockEdit]: false, // 禁用可能影响高度的块编辑功能
      [Crepe.Feature.Cursor]: true,
      [Crepe.Feature.ListItem]: true,   // 恢复列表功能
      [Crepe.Feature.LinkTooltip]: false,  // 禁用内置链接功能，避免与 WikiLink 冲突
      [Crepe.Feature.ImageBlock]: false, // 禁用可能影响高度的图片块功能
      [Crepe.Feature.Placeholder]: true,
      [Crepe.Feature.Table]: true,      // 恢复表格功能
      [Crepe.Feature.Latex]: false,
      [Crepe.Feature.CodeMirror]: false // 禁用CodeMirror，可能影响滚动
    }

    const crepe = new Crepe({
      root,
      defaultValue: '', // 始终使用空字符串，避免分屏问题
      features
    })

    // 先配置 WikiLink 上下文，然后注入完整插件集合
    crepe.editor.use(wikiLinkConfigCtx)

    // 预先设置 WikiLink 配置
    crepe.editor.config((ctx) => {
      console.log('📋 [DEBUG] 预设 WikiLink 配置')

      // 合并默认配置和用户配置
      const defaultConfig = ctx.get(wikiLinkConfigCtx.key)
      const mergedConfig = {
        ...defaultConfig,
        ...wikiLinkConfig,
        onPageClick,
        onLinkCreate,
        currentPageName
      }

      console.log('📋 [DEBUG] 最终配置:', mergedConfig)
      ctx.set(wikiLinkConfigCtx.key, mergedConfig)
    })

    // 注入转义处理插件（优先级最高）
    console.log('🔌 注入转义处理插件')
    crepe.editor.use(escapeHandlerPlugin)

    // 注入 WikiLink 插件（按正确顺序）
    console.log('🔌 注入 WikiLink 插件和智能输入规则')
    crepe.editor.use(wikiLinkNode)           // 节点定义
    crepe.editor.use(wikiLinkRemarkPlugin)   // Remark 解析

    // 注入智能输入规则集合
    smartInputRules.forEach((rule, index) => {
      console.log(`🎯 注入智能输入规则 ${index + 1}`)
      crepe.editor.use(rule)
    })

    crepe.editor.use(wikiLinkPreviewPlugin)  // 预览功能（已修复类型问题）
    crepe.editor.use(wikiLinkViewPlugin)     // 视图渲染（关键：提供点击功能）

    // 自动补全插件暂时禁用，类型问题待解决
    // try {
    //   crepe.editor.use(wikiLinkAutoCompletePlugin)  // 自动补全插件
    //   console.log('✅ 自动补全插件已启用')
    // } catch (error) {
    //   console.warn('⚠️ 自动补全插件启用失败:', error)
    // }

    // 暂时禁用预览插件的 action 调用，避免配置未就绪的问题
    // TODO: 在编辑器创建完成后再添加预览插件
    // crepe.editor.action((ctx) => {
    //   const config = ctx.get(wikiLinkConfigCtx.key)
    //   const previewPlugin = createPreviewTooltip(config)
    //   console.log('👁️ 预览插件已准备，配置:', config)
    // })

    // 设置只读模式
    if (readonly) {
      crepe.setReadonly(true)
    }

    // 精确计算编辑器可用高度
    const calculateEditorHeight = () => {
      const editorContainer = root.closest('.markdown-editor-container')
      if (!editorContainer) return 400 // 默认高度

      const containerRect = editorContainer.getBoundingClientRect()
      const headerElement = editorContainer.parentElement?.querySelector('.flex.items-center.justify-between')
      const headerHeight = headerElement?.getBoundingClientRect().height || 60

      return Math.max(300, containerRect.height - headerHeight - 32) // 减去padding
    }

    // 强制设置编辑器DOM样式以确保正确的高度和滚动行为
    const setupEditorStyles = () => {
      const availableHeight = calculateEditorHeight()

      // 查找所有可能的编辑器容器
      const containers = [
        root.querySelector('.milkdown'),
        root.querySelector('.crepe'),
        root.querySelector('[data-milkdown-root]'),
        root.querySelector('.editor')
      ].filter(Boolean)

      containers.forEach(container => {
        if (container) {
          container.style.height = `${availableHeight}px`
          container.style.maxHeight = `${availableHeight}px`
          container.style.display = 'flex'
          container.style.flexDirection = 'column'
          container.style.overflow = 'hidden'
          container.style.minHeight = '0'
        }
      })

      // 查找编辑区域
      const editableElements = [
        root.querySelector('.ProseMirror'),
        root.querySelector('[contenteditable="true"]'),
        root.querySelector('.editor-content')
      ].filter(Boolean)

      editableElements.forEach(element => {
        if (element) {
          const editorHeight = availableHeight - 40 // 减去工具栏等元素的高度

          // 关键修复：设置明确的像素高度，避免flex布局导致的坐标计算错误
          element.style.height = `${editorHeight}px`
          element.style.maxHeight = `${editorHeight}px`
          element.style.minHeight = `${editorHeight}px`

          // 强制启用滚动 - 关键修复
          element.style.setProperty('overflow', 'auto', 'important')
          element.style.setProperty('overflow-y', 'scroll', 'important')
          element.style.setProperty('overflow-x', 'hidden', 'important')

          // 确保滚动容器属性正确
          element.style.setProperty('overscroll-behavior', 'contain', 'important')
          element.style.setProperty('-webkit-overflow-scrolling', 'touch', 'important') // iOS滚动优化

          // 强制重新计算布局和坐标
          element.style.setProperty('position', 'relative', 'important')
          element.style.setProperty('box-sizing', 'border-box', 'important')
          element.style.setProperty('padding', '16px', 'important')

          // 确保可以交互和滚动
          element.style.setProperty('pointer-events', 'auto', 'important')
          element.style.setProperty('touch-action', 'pan-y', 'important')

          // 确保内容可以超出容器高度
          element.style.wordWrap = 'break-word'
          element.style.whiteSpace = 'pre-wrap'

          // 隐藏滚动条但保持滚动功能
          element.style.scrollbarWidth = 'none'
          element.style.msOverflowStyle = 'none'

          // 添加webkit滚动条隐藏
          const style = document.createElement('style')
          style.textContent = `
            .ProseMirror::-webkit-scrollbar,
            [contenteditable="true"]::-webkit-scrollbar {
              display: none !important;
            }
          `
          if (!document.head.querySelector('style[data-editor-scrollbar]')) {
            style.setAttribute('data-editor-scrollbar', 'true')
            document.head.appendChild(style)
          }



          // 强制ProseMirror重新计算坐标和视图
          if (element.pmView) {
            setTimeout(() => {
              // 强制重新计算编辑器的DOM坐标
              element.pmView.dom.style.height = `${editorHeight}px`
              element.pmView.updateState(element.pmView.state)
              // 触发重新布局
              element.pmView.dom.scrollTop = element.pmView.dom.scrollTop
            }, 50)
          }

          // 监听滚动事件，确保坐标同步
          element.addEventListener('scroll', () => {
            if (element.pmView) {
              // 强制更新光标位置
              const selection = element.pmView.state.selection
              element.pmView.dispatch(element.pmView.state.tr.setSelection(selection))
            }
          })
        }
      })
    }

    // 设置样式，延迟确保编辑器完全加载
    setTimeout(setupEditorStyles, 100)

    // 监听DOM变化，确保样式持续生效（减少频率避免闪动）
    let observerTimeout: NodeJS.Timeout | null = null
    const observer = new MutationObserver(() => {
      if (observerTimeout) clearTimeout(observerTimeout)
      observerTimeout = setTimeout(setupEditorStyles, 200)
    })
    observer.observe(root, { childList: true, subtree: false })

    // 监听窗口大小变化，重新计算编辑器高度
    const handleResize = () => {
      setTimeout(setupEditorStyles, 100)
    }
    window.addEventListener('resize', handleResize)

    // 清理函数
    const cleanup = () => {
      observer.disconnect()
      window.removeEventListener('resize', handleResize)
      // 清理添加的样式
      const styleElement = document.head.querySelector('style[data-editor-scrollbar]')
      if (styleElement) {
        styleElement.remove()
      }
    }

    // 监听内容变化
    if (onChange) {
      crepe.on((listener) => {
        listener.markdownUpdated((ctx, markdown) => {
          // 激进的转义字符清理 - 专门针对方括号
          const aggressiveCleanedMarkdown = markdown
            .replace(/\\\[/g, '[')
            .replace(/\\\]/g, ']')
            .replace(/\\\[\\\[/g, '[[')
            .replace(/\\\]\\\]/g, ']]')

          console.log('🧹 激进清理转义字符:', {
            原始长度: markdown.length,
            清理后长度: aggressiveCleanedMarkdown.length,
            是否有变化: markdown !== aggressiveCleanedMarkdown
          })

          // 立即调用onChange
          onChange(aggressiveCleanedMarkdown)

          // 只有在非程序化更新时才更新双向链接
          if (!isUpdatingProgrammaticallyRef.current) {
            console.log('📝 用户编辑触发双向链接更新')
            updateBidirectionalLinks(aggressiveCleanedMarkdown)
          } else {
            console.log('🔄 程序化更新，跳过双向链接更新')
          }

          // 如果启用了自动保存，设置定时器
          if (settings.autoSave && markdown !== lastSaveContentRef.current) {
            // 清除之前的定时器
            if (autoSaveTimerRef.current) {
              clearTimeout(autoSaveTimerRef.current)
            }

            // 设置新的自动保存定时器
            autoSaveTimerRef.current = setTimeout(() => {
              if (markdown !== lastSaveContentRef.current) {
                lastSaveContentRef.current = markdown
                // 这里可以触发自动保存事件
                console.log('Auto-saving content...', {
                  interval: settings.autoSaveInterval,
                  contentLength: markdown.length
                })
              }
            }, (settings.autoSaveInterval || 30) * 1000)
          }
        })
      })
    }

    // 自动补全功能已移除

    // 将cleanup函数存储到crepe实例上，以便后续清理
    crepe._cleanup = cleanup

    return crepe
  })

  // 清理定时器和observer
  useEffect(() => {
    return () => {
      if (autoSaveTimerRef.current) {
        clearTimeout(autoSaveTimerRef.current)
      }

      if (programmaticUpdateTimeoutRef.current) {
        clearTimeout(programmaticUpdateTimeoutRef.current)
      }

      // 清理DOM observer
      const crepe = get()
      if (crepe && crepe._cleanup) {
        crepe._cleanup()
      }
    }
  }, [get])

  // 处理内容更新 - 防重复更新
  useEffect(() => {
    const crepe = get()
    if (crepe && initialValue && initialValue !== lastContentRef.current) {
      // console.log('🔄 更新编辑器内容:', initialValue.substring(0, 50) + '...')
      // console.log('📝 上次内容:', lastContentRef.current.substring(0, 50) + '...')

      // 清除之前的超时
      if (programmaticUpdateTimeoutRef.current) {
        clearTimeout(programmaticUpdateTimeoutRef.current)
      }

      // 设置程序化更新标记
      isUpdatingProgrammaticallyRef.current = true

      // 使用Crepe的API更新内容
      try {
        // 清理转义字符后再更新
        const cleanedValue = cleanContent(initialValue)
        crepe.action(replaceAll(cleanedValue))
        lastContentRef.current = cleanedValue // 记录已更新的内容
        console.log('✅ 内容更新成功（程序化，已清理转义字符）')

        // 延迟重置标记，确保markdownUpdated回调完成
        programmaticUpdateTimeoutRef.current = setTimeout(() => {
          isUpdatingProgrammaticallyRef.current = false
          console.log('🔄 程序化更新标记已重置')
        }, 1000) // 增加到1秒确保稳定
      } catch (error) {
        console.error('❌ 更新内容失败:', error)
        isUpdatingProgrammaticallyRef.current = false
      }
    } else if (crepe && initialValue === lastContentRef.current) {
      // console.log('⏭️ 内容未变化，跳过更新')
    }
  }, [initialValue, get])

  // 配置 WikiLink
  useEffect(() => {
    const editor = get()
    if (editor && (wikiLinkConfig || onPageClick || onLinkCreate)) {
      editor.action((ctx) => {
        const config = ctx.get(wikiLinkConfigCtx.key)
        // 更新配置
        Object.assign(config, {
          ...wikiLinkConfig,
          onPageClick,
          onLinkCreate,
          // 设置当前页面名称用于双向链接
          currentPageName
        })
      })
    }
  }, [get, wikiLinkConfig, onPageClick, onLinkCreate, currentPageName])

  // 应用编辑器主题类
  const effectiveTheme = theme ?? settings.editorTheme
  const editorThemeClass = effectiveTheme === 'classic' ? 'editor-theme-classic' : 'editor-theme-dark'
  const focusModeClass = settings.focusMode ? 'editor-focus-mode' : ''

  return (
    <div className={`milkdown-editor h-full flex flex-col overflow-hidden editor-fixed-height ${editorThemeClass} ${focusModeClass}`}>
      <Milkdown />
    </div>
  )
}

// 主要的MarkdownEditor组件 - 使用官方最佳实践
export const MarkdownEditor: React.FC<MarkdownEditorProps> = ({
  initialValue = '',
  onChange,
  readonly = false,
  height = '100%',
  className = '',
  placeholder = '开始编写...',
  wikiLinkConfig,
  onPageClick,
  onLinkCreate,
  currentPageName,
  theme
}) => {
  return (
    <div className={`relative markdown-editor-container h-full overflow-hidden editor-fixed-height ${className}`}>
      <MilkdownProvider>
        <CrepeEditorCore
          initialValue={initialValue}
          onChange={onChange}
          readonly={readonly}
          placeholder={placeholder}
          wikiLinkConfig={wikiLinkConfig}
          onPageClick={onPageClick}
          onLinkCreate={onLinkCreate}
          currentPageName={currentPageName}
          theme={theme}
        />
      </MilkdownProvider>
    </div>
  )
}

export default MarkdownEditor
