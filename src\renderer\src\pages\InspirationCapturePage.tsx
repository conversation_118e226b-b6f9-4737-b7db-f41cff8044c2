import { useState, useEffect, useRef } from 'react'
import { useUIStore } from '../store/uiStore'
import type { InboxItem } from '../components/features/InboxItem'

// 完全透明窗口的全局样式
const globalStyle = `
  body {
    overflow: hidden !important;
    margin: 0 !important;
    padding: 0 !important;
    background: transparent !important;
  }
  html {
    overflow: hidden !important;
    background: transparent !important;
  }
  #root {
    background: transparent !important;
  }
`

export function InspirationCapturePage() {
  const [quickInputContent, setQuickInputContent] = useState('')
  const [isSubmittingQuickInput, setIsSubmittingQuickInput] = useState(false)
  const [showTagSuggestions, setShowTagSuggestions] = useState(false)
  const [tagSuggestionIndex, setTagSuggestionIndex] = useState(0)
  const [currentTagInput, setCurrentTagInput] = useState('')
  const inputRef = useRef<HTMLInputElement>(null)
  const { addNotification } = useUIStore()

  // 常用标签（基础标签）
  const baseTags = ['工作', '学习', '生活', '想法', '待办', '重要', '创意', '灵感']

  // 获取用户自定义标签
  const getUserTags = (): string[] => {
    try {
      const saved = localStorage.getItem('paolife-user-tags')
      return saved ? JSON.parse(saved) : []
    } catch {
      return []
    }
  }

  // 保存用户自定义标签
  const saveUserTag = (tag: string) => {
    try {
      const userTags = getUserTags()
      if (!userTags.includes(tag) && !baseTags.includes(tag)) {
        const updatedTags = [...userTags, tag]
        localStorage.setItem('paolife-user-tags', JSON.stringify(updatedTags))
      }
    } catch (error) {
      console.error('Failed to save user tag:', error)
    }
  }

  // 合并所有标签
  const commonTags = [...baseTags, ...getUserTags()]

  // 自动聚焦和应用全局样式
  useEffect(() => {
    // 应用全局样式
    const styleElement = document.createElement('style')
    styleElement.textContent = globalStyle
    document.head.appendChild(styleElement)

    // 应用透明模式类
    document.documentElement.classList.add('inspiration-transparent')
    document.body.classList.add('inspiration-transparent')
    const root = document.getElementById('root')
    if (root) {
      root.classList.add('inspiration-transparent')
    }

    if (inputRef.current) {
      inputRef.current.focus()
    }

    // 清理函数
    return () => {
      document.head.removeChild(styleElement)
      document.documentElement.classList.remove('inspiration-transparent')
      document.body.classList.remove('inspiration-transparent')
      if (root) {
        root.classList.remove('inspiration-transparent')
      }
    }
  }, [])

  // ESC键关闭窗口
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        e.preventDefault()
        e.stopPropagation()
        closeWindow()
      }
    }

    document.addEventListener('keydown', handleKeyDown, true)
    return () => document.removeEventListener('keydown', handleKeyDown, true)
  }, [])


  const closeWindow = () => {
    if (window.electronAPI?.window?.close) {
      window.electronAPI.window.close()
    }
  }

  const handleQuickInputSubmit = async () => {
    if (!quickInputContent.trim()) return

    setIsSubmittingQuickInput(true)
    try {
      // 提取标签和内容
      const content = quickInputContent.trim()
      const tags: string[] = []
      let cleanContent = content

      // 提取所有#标签
      const tagMatches = content.match(/#[\u4e00-\u9fa5\w]+/g)
      if (tagMatches) {
        tagMatches.forEach(match => {
          const tag = match.slice(1) // 移除#
          if (tag && !tags.includes(tag)) {
            tags.push(tag)
          }
        })
        // 从内容中移除标签
        cleanContent = content.replace(/#[\u4e00-\u9fa5\w]+/g, '').replace(/\s+/g, ' ').trim()
      }

      const newItem: InboxItem = {
        id: Date.now().toString(),
        content: cleanContent,
        type: 'idea',
        priority: 'medium',
        tags: tags,
        processed: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }

      // 保存到localStorage（与收件箱页面保持一致）
      const existingItems = JSON.parse(localStorage.getItem('paolife-inbox-items') || '[]')
      const updatedItems = [newItem, ...existingItems]
      localStorage.setItem('paolife-inbox-items', JSON.stringify(updatedItems))

      // 触发自定义事件通知收件箱页面刷新
      document.dispatchEvent(new CustomEvent('inspiration-added', { detail: newItem }))

      setQuickInputContent('')
      setShowTagSuggestions(false)
      setCurrentTagInput('')

      addNotification({
        type: 'success',
        title: '灵感已记录',
        message: tags.length > 0 ? `已添加到收件箱，包含${tags.length}个标签` : '已添加到收件箱'
      })

      // 关闭窗口
      closeWindow()
    } catch (error) {
      console.error('Failed to create quick input item:', error)
      addNotification({
        type: 'error',
        title: '记录失败',
        message: '请重试'
      })
    } finally {
      setIsSubmittingQuickInput(false)
    }
  }

  const handleQuickInputKeyDown = (e: React.KeyboardEvent) => {
    if (showTagSuggestions) {
      const filteredTags = commonTags.filter(tag => tag.toLowerCase().includes(currentTagInput.toLowerCase()))
      const hasNewTag = currentTagInput && !commonTags.some(tag => tag.toLowerCase().includes(currentTagInput.toLowerCase()))
      const totalOptions = filteredTags.length + (hasNewTag ? 1 : 0)

      if (e.key === 'ArrowDown') {
        e.preventDefault()
        setTagSuggestionIndex((prev) => (prev + 1) % totalOptions)
      } else if (e.key === 'ArrowUp') {
        e.preventDefault()
        setTagSuggestionIndex((prev) => (prev - 1 + totalOptions) % totalOptions)
      } else if (e.key === 'Enter') {
        e.preventDefault()
        // 如果当前输入了新标签且选中的是新标签选项，使用新标签
        if (hasNewTag && tagSuggestionIndex === filteredTags.length) {
          handleTagSelect(currentTagInput)
        } else if (filteredTags.length > 0 && tagSuggestionIndex < filteredTags.length) {
          // 否则使用选中的已有标签
          handleTagSelect(filteredTags[tagSuggestionIndex])
        }
      } else if (e.key === 'Escape') {
        setShowTagSuggestions(false)
        setCurrentTagInput('')
      }
    } else if (e.key === 'Enter' && e.shiftKey) {
      e.preventDefault()
      handleQuickInputSubmit()
    }
  }

  const handleQuickInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setQuickInputContent(value)

    // 检查是否输入了#来触发标签选择
    const lastChar = value[value.length - 1]
    const beforeLastChar = value[value.length - 2]

    if (lastChar === '#' && (beforeLastChar === ' ' || beforeLastChar === undefined || value.length === 1)) {
      setShowTagSuggestions(true)
      setTagSuggestionIndex(0)
      setCurrentTagInput('')
    } else if (showTagSuggestions) {
      // 如果正在显示标签建议，更新当前标签输入
      const hashIndex = value.lastIndexOf('#')
      if (hashIndex !== -1) {
        const tagInput = value.slice(hashIndex + 1)
        setCurrentTagInput(tagInput)

        // 如果输入了空格，完成标签输入
        if (tagInput.includes(' ')) {
          const tag = tagInput.split(' ')[0]
          if (tag) {
            handleTagSelect(tag)
          }
        }
      } else {
        setShowTagSuggestions(false)
        setCurrentTagInput('')
      }
    }
  }

  const handleTagSelect = (tag: string) => {
    // 如果是新标签，保存到用户标签列表
    if (!baseTags.includes(tag) && !getUserTags().includes(tag)) {
      saveUserTag(tag)
    }

    const hashIndex = quickInputContent.lastIndexOf('#')
    if (hashIndex !== -1) {
      const beforeHash = quickInputContent.slice(0, hashIndex)
      const afterTag = quickInputContent.slice(hashIndex + 1 + currentTagInput.length)
      setQuickInputContent(`${beforeHash}#${tag} ${afterTag}`)
    }
    setShowTagSuggestions(false)
    setCurrentTagInput('')
  }

  return (
    <div
      className="fixed inset-0 overflow-hidden p-3"
      onClick={(e) => {
        // 点击空白区域关闭窗口（但不包括输入框和标签下拉框）
        if (e.target === e.currentTarget) {
          closeWindow()
        }
      }}
    >
      {/* 现代化灵感输入框 - 内部增强设计 */}
      <div
        className="relative w-full h-14 bg-white/95 backdrop-blur-xl rounded-xl border-2 border-blue-300/70 overflow-visible"
        onClick={(e) => e.stopPropagation()} // 阻止点击输入框区域时关闭窗口
      >
        {/* 渐变背景装饰 */}
        <div className="absolute inset-0 bg-gradient-to-r from-blue-50/70 via-purple-50/50 to-pink-50/70 rounded-xl"></div>

        {/* 左侧图标 */}
        <div className="absolute left-4 top-1/2 -translate-y-1/2 z-10">
          <div className="w-6 h-6 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center">
            <svg className="w-3.5 h-3.5 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path
                fillRule="evenodd"
                d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                clipRule="evenodd"
              />
            </svg>
          </div>
        </div>

        {/* 输入框 */}
        <input
          ref={inputRef}
          type="text"
          value={quickInputContent}
          onChange={handleQuickInputChange}
          onKeyDown={handleQuickInputKeyDown}
          onClick={(e) => e.stopPropagation()} // 确保点击输入框不会关闭窗口
          placeholder="记录你的灵感想法..."
          className="w-full h-full pl-14 pr-20 text-base font-medium text-gray-800 placeholder-gray-500 bg-transparent border-0 focus:outline-none relative z-10 rounded-xl focus:placeholder-gray-400 transition-all duration-200"
          disabled={isSubmittingQuickInput}
        />

        {/* 右侧提示文字 */}
        <div className="absolute right-4 top-1/2 -translate-y-1/2 z-10">
          <span className="text-sm text-gray-400 font-medium">⇧ + ↵</span>
        </div>

        {/* 底部微光效果 */}
        <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-blue-400/60 to-transparent"></div>

        {/* 现代化标签建议下拉框 */}
        {showTagSuggestions && (
          <div
            className="absolute top-full left-0 right-0 mt-2 bg-white/95 backdrop-blur-xl border-2 border-blue-300/70 rounded-xl z-50 max-h-48 overflow-hidden"
            onClick={(e) => e.stopPropagation()} // 阻止点击标签下拉框时关闭窗口
          >
            {/* 顶部装饰线 */}
            <div className="h-px bg-gradient-to-r from-transparent via-blue-300/50 to-transparent"></div>

            <div className="max-h-44 overflow-y-auto scrollbar-hide">
              {commonTags
                .filter((tag) => tag.toLowerCase().includes(currentTagInput.toLowerCase()))
                .map((tag, index) => (
                  <div
                    key={tag}
                    className={`px-4 py-3 cursor-pointer text-sm font-medium transition-all duration-200 ${
                      index === tagSuggestionIndex
                        ? 'bg-gradient-to-r from-blue-50 to-purple-50 text-blue-700 border-l-2 border-blue-400'
                        : 'hover:bg-gray-50/80 text-gray-700'
                    }`}
                    onClick={() => handleTagSelect(tag)}
                  >
                    <div className="flex items-center gap-2">
                      <span className="text-blue-500">#</span>
                      <span>{tag}</span>
                    </div>
                  </div>
                ))}
              {currentTagInput &&
                !commonTags.some((tag) =>
                  tag.toLowerCase().includes(currentTagInput.toLowerCase())
                ) && (
                  <div
                    className={`px-4 py-3 cursor-pointer text-sm font-medium transition-all duration-200 ${
                      tagSuggestionIndex ===
                      commonTags.filter((tag) =>
                        tag.toLowerCase().includes(currentTagInput.toLowerCase())
                      ).length
                        ? 'bg-gradient-to-r from-green-50 to-emerald-50 text-green-700 border-l-2 border-green-400'
                        : 'hover:bg-gray-50/80 text-gray-700'
                    }`}
                    onClick={() => handleTagSelect(currentTagInput)}
                  >
                    <div className="flex items-center gap-2">
                      <span className="text-green-500">#</span>
                      <span>{currentTagInput}</span>
                      <span className="text-xs text-gray-400 ml-auto bg-gray-100 px-2 py-1 rounded-full">
                        新标签
                      </span>
                    </div>
                  </div>
                )}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default InspirationCapturePage
