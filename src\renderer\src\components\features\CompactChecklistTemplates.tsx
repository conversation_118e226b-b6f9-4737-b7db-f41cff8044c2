import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { Plus, CheckSquare, Play, Edit, Trash2 } from 'lucide-react'
import { useTaskStore } from '../../store/taskStore'
import { electronApi } from '../../lib/api'
import { useLanguage } from '../../contexts/LanguageContext'
import { useUIStore } from '../../store/uiStore'
import { useConfirmDialog } from '../shared/ConfirmDialog'
import type { Checklist, ChecklistInstance } from '../../../../shared/types'

interface CompactChecklistTemplatesProps {
  areaId: string
  onCreateTemplate?: () => void
  className?: string
}

export function CompactChecklistTemplates({ 
  areaId, 
  onCreateTemplate, 
  className 
}: CompactChecklistTemplatesProps) {
  const { t } = useLanguage()
  const { addNotification } = useUIStore()
  const { confirm, ConfirmDialog } = useConfirmDialog()
  
  const {
    checklists,
    checklistInstances,
    addChecklistInstance,
    deleteChecklist
  } = useTaskStore()

  // 获取该领域的清单模板
  const areaTemplates = checklists ? checklists.filter(checklist =>
    (checklist as any).areaId === areaId
  ) : []

  // 使用模板创建实例
  const handleUseTemplate = async (template: Checklist) => {
    try {
      const result = await electronApi.database.createChecklistInstance({
        checklistId: template.id,
        status: template.template.map(item => ({
          id: item.id,
          text: item.text,
          completed: false
        })),
        templateSnapshot: {
          name: template.name,
          createdAt: template.createdAt
        }
      })

      if (result.success) {
        // 添加到本地状态
        addChecklistInstance(result.data)
        addNotification({
          type: 'success',
          title: t('components.checklistTemplates.notifications.created'),
          message: t('components.checklistTemplates.notifications.createdMessage', { name: template.name })
        })
      } else {
        addNotification({
          type: 'error',
          title: t('components.checklistTemplates.notifications.createFailed'),
          message: result.error || t('components.checklistTemplates.notifications.createFailedMessage')
        })
      }
    } catch (error) {
      console.error('Error creating checklist instance:', error)
      addNotification({
        type: 'error',
        title: t('components.checklistTemplates.notifications.createFailed'),
        message: t('components.checklistTemplates.notifications.createFailedMessage')
      })
    }
  }

  // 删除模板
  const handleDeleteTemplate = async (template: Checklist) => {
    // 检查是否有进行中的实例
    const activeInstances = checklistInstances.filter(
      instance => instance.checklistId === template.id && !instance.completedAt
    )

    if (activeInstances.length > 0) {
      addNotification({
        type: 'warning',
        title: t('components.checklistTemplates.notifications.cannotDelete'),
        message: t('components.checklistTemplates.notifications.hasActiveInstances', { count: activeInstances.length })
      })
      return
    }

    const confirmed = await confirm({
      title: t('components.checklistTemplates.deleteConfirm.title'),
      message: t('components.checklistTemplates.deleteConfirm.message', { name: template.name }),
      confirmText: t('common.delete'),
      cancelText: t('common.cancel')
    })

    if (confirmed) {
      deleteChecklist(template.id)
      addNotification({
        type: 'success',
        title: t('components.checklistTemplates.notifications.deleted'),
        message: t('components.checklistTemplates.notifications.deletedMessage')
      })
    }
  }

  // 获取模板的使用统计
  const getTemplateStats = (templateId: string) => {
    const instances = checklistInstances.filter(instance => instance.checklistId === templateId)
    const completed = instances.filter(instance => instance.completedAt).length
    const active = instances.filter(instance => !instance.completedAt).length
    
    return { total: instances.length, completed, active }
  }

  return (
    <>
      <Card className={className}>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <CheckSquare className="h-5 w-5" />
                {t('components.checklistTemplates.title')}
              </CardTitle>
              <CardDescription>
                {t('components.checklistTemplates.description')}
              </CardDescription>
            </div>
            <Button variant="outline" size="sm" onClick={onCreateTemplate}>
              <Plus className="h-4 w-4 mr-2" />
              {t('components.checklistTemplates.newTemplate')}
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {areaTemplates.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <CheckSquare className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p className="text-sm">{t('components.checklistTemplates.noTemplates')}</p>
              <p className="text-xs mt-1">{t('components.checklistTemplates.createHint')}</p>
            </div>
          ) : (
            <div className="space-y-3">
              {areaTemplates.map((template) => {
                const stats = getTemplateStats(template.id)
                
                return (
                  <Card key={template.id} className="border-l-4 border-l-green-500">
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex-1 min-w-0">
                          <h4 className="text-sm font-medium truncate mb-1">
                            {template.name}
                          </h4>
                          <div className="flex items-center gap-2 text-xs text-muted-foreground">
                            <span>{t('components.checklistTemplates.itemsCount', { count: template.template.length })}</span>
                            {stats.total > 0 && (
                              <>
                                <span>•</span>
                                <span>{t('components.checklistTemplates.usedTimes', { count: stats.total })}</span>
                              </>
                            )}
                            {stats.active > 0 && (
                              <>
                                <span>•</span>
                                <Badge variant="secondary" className="text-xs px-1 py-0">
                                  {t('components.checklistTemplates.activeCount', { count: stats.active })}
                                </Badge>
                              </>
                            )}
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-1 ml-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteTemplate(template)}
                            className="text-muted-foreground hover:text-destructive p-1 h-auto"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>

                      {/* 模板项目预览 */}
                      <div className="mb-3">
                        <div className="text-xs text-muted-foreground mb-1">{t('components.checklistTemplates.containsItems')}:</div>
                        <div className="space-y-1">
                          {template.template.slice(0, 3).map((item) => (
                            <div key={item.id} className="flex items-center gap-2 text-xs">
                              <div className="w-3 h-3 border border-muted-foreground/30 rounded-sm flex-shrink-0" />
                              <span className="truncate">{item.text}</span>
                            </div>
                          ))}
                          {template.template.length > 3 && (
                            <div className="text-xs text-muted-foreground pl-5">
                              {t('components.checklistTemplates.moreItems', { count: template.template.length - 3 })}
                            </div>
                          )}
                        </div>
                      </div>

                      {/* 使用按钮 */}
                      <Button
                        onClick={() => handleUseTemplate(template)}
                        className="w-full bg-green-600 hover:bg-green-700 text-white"
                        size="sm"
                      >
                        <Play className="h-4 w-4 mr-2" />
                        <span className="font-medium">{t('components.checklistTemplates.useTemplate')}</span>
                      </Button>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          )}
        </CardContent>
      </Card>
      
      <ConfirmDialog />
    </>
  )
}

export default CompactChecklistTemplates
