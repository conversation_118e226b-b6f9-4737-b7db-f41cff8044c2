import { Outlet, useLocation } from 'react-router-dom'
import React, { useState, useEffect, useCallback, useMemo } from 'react'
// 移除未使用的 shared 组件导入
import { Button } from '../components/ui/button'
import FileTree from '../components/features/FileTree'
import MarkdownEditor from '../components/features/MarkdownEditor'

import {
  BidirectionalBacklinks,
  BidirectionalOutlinks,
  LinkStatisticsCard
} from '../components/features/BidirectionalLinks'
import { bidirectionalLinkService } from '../services/bidirectionalLinkService'
import { useLanguage } from '../contexts/LanguageContext'
import UnifiedReferencePanel from '../components/features/UnifiedReferencePanel'
import ReferenceAnalyticsDashboard from '../components/features/ReferenceAnalyticsDashboard'
import { UnifiedReference } from '../services/unifiedReferenceService'

import type { FileTreeItem } from '../components/features/FileTreeNode'
import { fileSystemApi } from '../lib/api'
import { useUIStore } from '../store/uiStore'

import { useUserSettingsStore } from '../store/userSettingsStore'
import { DeleteConfirmDialog } from '../components/features/DeleteConfirmDialog'
import { useConfirmDialog } from '../components/shared/ConfirmDialog'
import { getResourcesPath, createPage, normalizePageName } from '../plugins/wikilink/utils'
import { cn } from '../lib/utils'

// 简单的路径拼接函数，用于替代Node.js的path.join
const joinPath = (...parts: string[]): string => {
  return parts
    .map((part) => part.replace(/^\/+|\/+$/g, '')) // 移除开头和结尾的斜杠
    .filter((part) => part.length > 0)
    .join('/')
}

// 将虚拟路径转换为文件系统路径
const convertToRealPath = async (virtualPath: string, userSettings?: any): Promise<string> => {
  let basePath: string

  if (userSettings?.workspaceDirectory) {
    // 使用用户设置的工作目录
    basePath = joinPath(userSettings.workspaceDirectory, 'PaoLife')
  } else {
    // 使用默认的用户数据目录
    const userDataPath = await window.electronAPI.app.getPath('userData')
    basePath = joinPath(userDataPath, 'resources')
  }

  // 移除开头的斜杠，构建相对路径
  const relativePath = virtualPath.replace(/^\/+/, '')

  // 如果是根路径，直接返回基础目录
  if (!relativePath) {
    return basePath
  }

  // 构建完整路径：基础目录/相对路径
  return joinPath(basePath, relativePath)
}

export function ResourcesPage() {
  const location = useLocation()
  const isDetailView = location.pathname !== '/resources'
  const [selectedFile, setSelectedFile] = useState<FileTreeItem | null>(null)
  const [showBacklinks, setShowBacklinks] = useState(true)  // 默认显示引用面板
  const [showAnalytics, setShowAnalytics] = useState(false)
  const [refreshTrigger, setRefreshTrigger] = useState(0)
  const [bidirectionalBacklinks, setBidirectionalBacklinks] = useState<any[]>([])
  const [bidirectionalOutlinks, setBidirectionalOutlinks] = useState<any[]>([])
  const [linkStatistics, setLinkStatistics] = useState<any>(null)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [itemToDelete, setItemToDelete] = useState<FileTreeItem | null>(null)
  const [isDeleting, setIsDeleting] = useState(false)

  // 编辑器相关状态
  const [currentFileContent, setCurrentFileContent] = useState<string>('')
  const [isEditorReady, setIsEditorReady] = useState(false)
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)
  const [currentEditorContent, setCurrentEditorContent] = useState<string>('')

  const { t } = useLanguage()
  const { addNotification, setLoadingState, clearLoadingState } = useUIStore()

  const { settings, setFocusMode } = useUserSettingsStore()
  const { confirm, ConfirmDialog: CreateFileConfirmDialog } = useConfirmDialog()
  // const { openFile } = useEditorStore() // Removed

  // 专注模式状态
  const isFocusMode = settings.focusMode || false

  // 处理编辑器内容变化
  const handleEditorChange = useCallback((markdown: string) => {
    setCurrentEditorContent(markdown)
    setHasUnsavedChanges(true)
    // 这里可以添加自动保存逻辑
  }, [])

  // 解析页面路径
  const resolvePagePath = useCallback(async (pageName: string): Promise<string> => {
    const resourcesPath = await getResourcesPath(settings)
    const normalizedName = normalizePageName(pageName)
    return `${resourcesPath}/${normalizedName}.md`
  }, [settings])

  // 打开现有文件
  const openExistingFile = useCallback(async (filePath: string) => {
    try {
      console.log('🔗 [openExistingFile] 开始打开文件:', filePath)

      const result = await fileSystemApi.readFile({ path: filePath })
      if (result.success && result.data) {
        const content = result.data.content || ''

        // 先更新内容状态
        setCurrentFileContent(content)
        setCurrentEditorContent(content)
        setHasUnsavedChanges(false)

        // 创建一个虚拟的 FileTreeItem 来更新选中状态
        const fileName = filePath.split('/').pop() || ''
        const resourcesPath = await getResourcesPath(settings)
        const virtualPath = filePath.replace(resourcesPath, '').replace(/^\//, '')

        console.log('🔗 [openExistingFile] 路径计算:', {
          原始路径: filePath,
          资源根路径: resourcesPath,
          虚拟路径: virtualPath,
          文件名: fileName
        })

        const virtualFile: FileTreeItem = {
          id: `wikilink-${Date.now()}`,
          name: fileName,
          path: `/${virtualPath}`,
          type: 'file',
          children: [],
          modifiedAt: new Date().toISOString(),
          createdAt: new Date().toISOString()
        }

        console.log('🔗 [openExistingFile] 创建的虚拟文件对象:', virtualFile)

        // 最后更新选中文件（这会触发双向链接加载）
        setSelectedFile(virtualFile)

        addNotification({
          type: 'success',
          title: t('pages.resources.notifications.wikiLinkSuccess'),
          message: t('pages.resources.notifications.wikiLinkSuccessMessage', { fileName })
        })
      } else {
        throw new Error(result.error || t('pages.resources.notifications.readFileFailedMessage'))
      }
    } catch (error) {
      console.error('打开文件失败:', error)
      addNotification({
        type: 'error',
        title: t('pages.resources.notifications.openFileFailed'),
        message: error instanceof Error ? error.message : t('pages.resources.notifications.openFileFailedMessage')
      })
    }
  }, [settings, addNotification])

  // 创建并打开新文件
  const createAndOpenNewFile = useCallback(async (filePath: string, pageName: string) => {
    try {
      const resourcesPath = await getResourcesPath(settings)
      const normalizedName = normalizePageName(pageName)
      const defaultContent = `# ${pageName}\n\n${t('pages.resources.createTime', { time: new Date().toLocaleString() })}\n`

      const success = await createPage(normalizedName, resourcesPath, defaultContent)
      if (success) {
        await openExistingFile(filePath)
        addNotification({
          type: 'success',
          title: t('pages.resources.notifications.fileCreated'),
          message: t('pages.resources.notifications.fileCreatedMessage', { fileName: `${pageName}.md` })
        })
      } else {
        throw new Error(t('pages.resources.notifications.createFileFailedMessage'))
      }
    } catch (error) {
      console.error('创建文件失败:', error)
      addNotification({
        type: 'error',
        title: t('pages.resources.notifications.createFileFailed'),
        message: error instanceof Error ? error.message : t('pages.resources.notifications.createFileFailedMessage')
      })
    }
  }, [settings, addNotification, openExistingFile])

  // WikiLink 智能跳转处理
  const handleWikiLinkClick = useCallback(async (pageName: string) => {
    try {
      const filePath = await resolvePagePath(pageName)
      const existsResult = await fileSystemApi.fileExists(filePath)

      if (existsResult.success && existsResult.data) {
        // 文件存在，直接打开
        await openExistingFile(filePath)
      } else {
        // 文件不存在，显示创建确认对话框
        const ok = await confirm({
          title: t('pages.resources.createNewFile'),
          description: t('pages.resources.createNewFileDescription', { pageName }),
          variant: 'default',
          confirmText: t('pages.resources.createFile'),
          cancelText: t('pages.resources.cancel')
        })
        if (ok) {
          await createAndOpenNewFile(filePath, pageName)
        }
      }
    } catch (error) {
      console.error('WikiLink 跳转失败:', error)
      addNotification({
        type: 'error',
        title: t('pages.resources.notifications.wikiLinkFailed'),
        message: error instanceof Error ? error.message : t('pages.resources.notifications.wikiLinkFailedMessage')
      })
    }
  }, [resolvePagePath, openExistingFile, createAndOpenNewFile, confirm, addNotification])

  // 处理统一引用点击事件
  const handleUnifiedReferenceClick = useCallback(async (reference: UnifiedReference) => {
    console.log('🔗 统一引用点击事件:', reference)

    try {
      switch (reference.referenceType) {
        case 'wikilink':
          // WikiLink 引用，跳转到源文档
          const sourcePath = reference.sourcePath || reference.sourceId
          if (sourcePath) {
            const fullPath = await convertToRealPath(sourcePath, settings)
            await openExistingFile(fullPath)
          }
          break

        case 'description':
        case 'task':
          // 项目/任务引用，跳转到项目管理页面
          console.log('🚀 跳转到项目:', reference.sourceId)
          addNotification({
            type: 'info',
            title: '功能开发中',
            message: '项目引用跳转功能正在开发中'
          })
          break

        case 'note':
          // 领域引用，跳转到领域页面
          console.log('🏷️ 跳转到领域:', reference.sourceId)
          addNotification({
            type: 'info',
            title: '功能开发中',
            message: '领域引用跳转功能正在开发中'
          })
          break

        default:
          console.warn('⚠️ 未知的引用类型:', reference.referenceType)
      }
    } catch (error) {
      console.error('❌ 统一引用跳转失败:', error)
      addNotification({
        type: 'error',
        title: '跳转失败',
        message: error instanceof Error ? error.message : '跳转失败'
      })
    }
  }, [settings, addNotification, openExistingFile])

  // WikiLink 配置
  const wikiLinkConfig = useMemo(() => {
    // console.log('📋 [DEBUG] 创建 WikiLink 配置，当前 settings:', settings)

    const config = {
      enableAutoComplete: false,  // 自动补全功能已移除
      enablePreview: true,  // 启用预览功能
      enableBidirectionalLinks: true,
      previewMaxLines: 5,
      autoCreatePages: false,
      readPageContent: async (pageName: string) => {
        // console.log('📋 [DEBUG] readPageContent 被调用，页面名称:', pageName)
        try {
          const filePath = await resolvePagePath(pageName)
          // console.log('📋 [DEBUG] 解析的文件路径:', filePath)

          // 总是读取文件的最新内容，确保预览显示最新状态
          // 移除了直接返回编辑器内容的逻辑，以确保预览始终显示文件的最新保存状态

          // 尝试读取文件，添加重试机制
          let retries = 2
          while (retries > 0) {
            try {
              const result = await fileSystemApi.readFile({ path: filePath })
              if (result.success && result.data) {
                const content = result.data.content || ''
                // console.log('📋 [DEBUG] 读取到的完整内容长度:', content.length)
                // console.log('📋 [DEBUG] 内容前100字符:', content.substring(0, 100))
                // 返回完整内容，让预览编辑器自己处理显示
                return content
              }
              // console.warn('⚠️ [DEBUG] 文件读取失败或无内容')
              return ''
            } catch (fileError: any) {
              retries--
              if (retries > 0 && fileError.message?.includes('locked')) {
                // console.log('📋 [DEBUG] 文件被锁定，等待重试...')
                await new Promise(resolve => setTimeout(resolve, 100))
                continue
              }
              throw fileError
            }
          }
          return ''
        } catch (error) {
          console.error('❌ [DEBUG] 读取页面内容失败:', error)
          return t('pages.resources.previewLoadFailed', { pageName })
        }
      },
      savePageContent: async (pageName: string, content: string) => {
        // console.log('📋 [DEBUG] savePageContent 被调用，页面名称:', pageName)
        try {
          const filePath = await resolvePagePath(pageName)
          // console.log('📋 [DEBUG] 保存文件路径:', filePath)

          const result = await fileSystemApi.writeFile({
            path: filePath,
            content: content
          })

          if (result.success) {
            console.log('📋 [DEBUG] 文件保存成功')

            // 如果保存的是当前打开的文件，更新编辑器内容
            if (selectedFile && selectedFile.path === filePath) {
              setCurrentEditorContent(content)
            }

            // 显示保存成功通知
            addNotification({
              type: 'success',
              title: t('pages.resources.notifications.saveSuccess'),
              message: t('pages.resources.notifications.saveSuccessMessage', { fileName: pageName })
            })
          } else {
            throw new Error(result.error || t('pages.resources.notifications.saveFailed'))
          }
        } catch (error) {
          console.error('❌ [DEBUG] 保存页面内容失败:', error)
          addNotification({
            type: 'error',
            title: t('pages.resources.notifications.saveFailed'),
            message: error instanceof Error ? error.message : t('pages.resources.notifications.saveFailedMessage')
          })
          throw error
        }
      }
    }

    // console.log('📋 [DEBUG] WikiLink 配置创建完成:', {
    //   enableAutoComplete: config.enableAutoComplete,
    //   enablePreview: config.enablePreview,
    //   hasReadPageContent: !!config.readPageContent
    // })

    return config
  }, [settings, resolvePagePath, selectedFile, currentEditorContent, addNotification, setCurrentEditorContent])

  // 保存当前文件
  const handleSaveFile = useCallback(async (content: string) => {
    if (!selectedFile || selectedFile.type !== 'file') {
      addNotification({
        type: 'warning',
        title: t('pages.resources.notifications.cannotSave'),
        message: t('pages.resources.notifications.cannotSaveMessage')
      })
      return
    }

    const loadingKey = 'file-save'
    setLoadingState(loadingKey, true)

    try {
      // 将虚拟路径转换为真实路径
      const realPath = await convertToRealPath(selectedFile.path, settings)

      // 保存文件内容
      const result = await fileSystemApi.writeFile({
        path: realPath,
        content: content,
        createDirs: false
      })

      if (result.success) {
        setHasUnsavedChanges(false)
        addNotification({
          type: 'success',
          title: t('pages.resources.notifications.saveSuccess'),
          message: t('pages.resources.notifications.saveSuccessMessage', { fileName: selectedFile.name })
        })
      } else {
        addNotification({
          type: 'error',
          title: t('pages.resources.notifications.saveFailed'),
          message: result.error || t('pages.resources.notifications.saveFailedMessage')
        })
      }
    } catch (error) {
      console.error('保存文件失败:', error)
      addNotification({
        type: 'error',
        title: t('pages.resources.notifications.saveFailed'),
        message: error instanceof Error ? error.message : t('pages.resources.notifications.saveFailedMessage')
      })
    } finally {
      clearLoadingState(loadingKey)
    }
  }, [selectedFile, settings, addNotification, setLoadingState, clearLoadingState])

  // 加载双向链接数据
  const loadBidirectionalLinks = useCallback(async () => {
    if (!selectedFile || selectedFile.type !== 'file') {
      setBidirectionalBacklinks([])
      setBidirectionalOutlinks([])
      setLinkStatistics(null)
      return
    }

    try {
      console.log('🔄 开始加载双向链接数据，文档路径:', selectedFile.path)
      const { backlinks, outlinks, statistics } = await bidirectionalLinkService.getDocumentLinks(selectedFile.path)

      setBidirectionalBacklinks(backlinks)
      setBidirectionalOutlinks(outlinks)
      setLinkStatistics(statistics)

      console.log('🔧 状态设置完成:', {
        设置的反向链接: backlinks.length,
        设置的出链: outlinks.length,
        反向链接数据: backlinks,
        出链数据: outlinks
      })

      console.log('✅ 双向链接数据加载完成:', {
        文档: selectedFile.path,
        反向链接: backlinks.length,
        出链: outlinks.length,
        统计: statistics
      })

      console.log('🔍 查询双向链接的路径:', selectedFile.path)

      if (backlinks.length > 0) {
        console.log('📥 反向链接详情:', backlinks)
      }
      if (outlinks.length > 0) {
        console.log('📤 出链详情:', outlinks)
      }

      // 检查状态更新
      console.log('🔄 状态更新检查:', {
        bidirectionalBacklinks: bidirectionalBacklinks.length,
        bidirectionalOutlinks: bidirectionalOutlinks.length,
        linkStatistics: linkStatistics
      })
    } catch (error) {
      console.error('❌ 加载双向链接数据失败:', error)
      setBidirectionalBacklinks([])
      setBidirectionalOutlinks([])
      setLinkStatistics(null)
    }
  }, [selectedFile])

  // 移除防抖函数，双向链接更新现在由MarkdownEditor内部处理

  // 检查是否需要自动打开指定的资源文件
  useEffect(() => {
    const openResourcePath = sessionStorage.getItem('openResourcePath')
    if (openResourcePath) {
      // 清除 sessionStorage
      sessionStorage.removeItem('openResourcePath')

      // 自动打开文件
      openExistingFile(openResourcePath)

      console.log(`🔗 [自动打开] 打开资源文件: ${openResourcePath}`)
    }
  }, [openExistingFile])

  // 当选中文件变化时加载双向链接
  useEffect(() => {
    loadBidirectionalLinks()
  }, [selectedFile, loadBidirectionalLinks])

  // 专注模式切换函数
  const toggleFocusMode = useCallback((): void => {
    setFocusMode(!isFocusMode)
  }, [isFocusMode, setFocusMode])

  // 键盘快捷键支持
  React.useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent): void => {
      // F11 或 Ctrl+Shift+F 切换专注模式
      if (event.key === 'F11' || (event.ctrlKey && event.shiftKey && event.key === 'F')) {
        event.preventDefault()
        toggleFocusMode()
      }

      // Ctrl+S 保存文件
      if (event.ctrlKey && event.key === 's') {
        event.preventDefault()
        if (selectedFile && selectedFile.type === 'file' && selectedFile.name.endsWith('.md') && hasUnsavedChanges) {
          handleSaveFile(currentEditorContent)
        }
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [isFocusMode, toggleFocusMode, selectedFile, hasUnsavedChanges, currentEditorContent, handleSaveFile])

  if (isDetailView) {
    return <Outlet />
  }

  const handleFileSelect = async (file: FileTreeItem) => {
    setSelectedFile(file)
    console.log('Selected file:', file)

    // 如果是Markdown文件，在编辑器中打开
    if (file.type === 'file' && file.name.endsWith('.md')) {
      try {
        // 将虚拟路径转换为真实路径
        const realPath = await convertToRealPath(file.path, settings)

        // 读取文件内容
        const result = await fileSystemApi.readFile({ path: realPath })
        // console.log('已读取文件内容:', result.data?.content)
        if (result.success && result.data) {
          // 设置编辑器内容
          const content = result.data.content || ''
          setCurrentFileContent(content)
          setCurrentEditorContent(content) // 同步编辑器内容状态
          setHasUnsavedChanges(false)
          // console.log('文件已加载到编辑器:', file.name)
        } else {
          addNotification({
            type: 'error',
            title: t('pages.resources.notifications.readFileFailed'),
            message: result.error || t('pages.resources.notifications.readFileFailedMessage')
          })
        }
      } catch (error) {
        console.error('打开文件失败:', error)
        addNotification({
          type: 'error',
          title: t('pages.resources.notifications.openFileFailed'),
          message: error instanceof Error ? error.message : t('pages.resources.notifications.openFileFailedMessage')
        })
      }
    } else {
      // 如果不是Markdown文件，清空编辑器
      setCurrentFileContent('')
      setCurrentEditorContent('')
      setHasUnsavedChanges(false)
    }
  }

  const handleFileCreate = async (parentPath: string, name: string, type: 'file' | 'folder') => {
    const loadingKey = 'file-create'
    setLoadingState(loadingKey, true)

    try {
      // 将虚拟路径转换为真实的文件系统路径
      const realParentPath = await convertToRealPath(parentPath, settings)

      if (type === 'file') {
        // 确保文件有.md扩展名（如果没有的话）
        const fileName = name.endsWith('.md') ? name : `${name}.md`
        const filePath = joinPath(realParentPath, fileName)

        // 检查文件是否已存在
        const existsResult = await fileSystemApi.fileExists(filePath)
        if (existsResult.success && existsResult.data) {
          addNotification({
            type: 'warning',
            title: t('pages.resources.notifications.fileExists'),
            message: t('pages.resources.notifications.fileExistsMessage', { fileName })
          })
          return
        }

        const defaultContent = `# ${name}\n\n${t('pages.resources.createTime', { time: new Date().toLocaleString() })}\n`
        await fileSystemApi.writeFile({
          path: filePath,
          content: defaultContent,
          createDirs: true
        })

        addNotification({
          type: 'success',
          title: t('pages.resources.notifications.fileCreated'),
          message: t('pages.resources.notifications.fileCreatedMessage', { fileName })
        })

        // 编辑器已移除，仅记录文件创建
        console.log('文件已创建:', fileName)
      } else {
        const folderPath = joinPath(realParentPath, name)

        // 检查文件夹是否已存在
        const existsResult = await fileSystemApi.fileExists(folderPath)
        if (existsResult.success && existsResult.data) {
          addNotification({
            type: 'warning',
            title: t('pages.resources.notifications.folderExists'),
            message: t('pages.resources.notifications.folderExistsMessage', { name })
          })
          return
        }

        await fileSystemApi.createDirectory(folderPath)

        addNotification({
          type: 'success',
          title: t('pages.resources.notifications.folderCreated'),
          message: t('pages.resources.notifications.folderCreatedMessage', { name })
        })
      }

      // 触发文件树刷新
      setRefreshTrigger((prev) => prev + 1)
    } catch (error) {
      console.error('Failed to create', type, ':', error)
      addNotification({
        type: 'error',
        title: t('pages.resources.notifications.createFailed'),
        message:
          error instanceof Error ? error.message : t('pages.resources.notifications.createFailedMessage', { type: type === 'file' ? t('pages.resources.file') : t('common.folder') })
      })
    } finally {
      clearLoadingState(loadingKey)
    }
  }



  // 处理删除请求（显示确认对话框）
  const handleFileDelete = (item: FileTreeItem) => {
    setItemToDelete(item)
    setDeleteDialogOpen(true)
  }

  // 确认删除
  const handleDeleteConfirm = async () => {
    if (!itemToDelete) return

    setIsDeleting(true)

    try {
      // 将虚拟路径转换为真实路径
      const realPath = await convertToRealPath(itemToDelete.path, settings)
      console.log('Deleting:', realPath)

      let result: any
      if (itemToDelete.type === 'file') {
        result = await fileSystemApi.deleteFile(realPath)
      } else {
        result = await fileSystemApi.deleteDirectory(realPath)
      }

      if (result.success) {
        addNotification({
          type: 'success',
          title: t('pages.resources.notifications.deleteSuccess'),
          message: t('pages.resources.notifications.deleteSuccessMessage', {
            type: itemToDelete.type === 'file' ? t('pages.resources.file') : t('common.folder'),
            name: itemToDelete.name
          })
        })

        // 如果删除的是当前选中的文件，清除选择
        if (selectedFile && selectedFile.path === itemToDelete.path) {
          setSelectedFile(null)
        }

        // 刷新文件树
        setRefreshTrigger((prev) => prev + 1)
      } else {
        addNotification({
          type: 'error',
          title: t('pages.resources.notifications.deleteFailed'),
          message: result.error || t('common.unknown')
        })
      }
    } catch (error) {
      addNotification({
        type: 'error',
        title: t('pages.resources.notifications.deleteFailed'),
        message: t('pages.resources.notifications.deleteFailedMessage')
      })
    } finally {
      setIsDeleting(false)
      setDeleteDialogOpen(false)
      setItemToDelete(null)
    }
  }

  // 取消删除
  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false)
    setItemToDelete(null)
  }

  // 处理文件/文件夹重命名
  const handleFileRename = async (item: any, newName: string) => {
    const loadingKey = 'file-rename'
    setLoadingState(loadingKey, true)

    try {
      console.log('Renaming item:', item)
      console.log('New name:', newName)

      // 将虚拟路径转换为真实路径
      const oldRealPath = await convertToRealPath(item.path, settings)
      console.log('Old real path:', oldRealPath)

      // 构建新的路径
      const parentPath = item.path.substring(0, item.path.lastIndexOf('/'))
      const newVirtualPath = parentPath ? `${parentPath}/${newName}` : `/${newName}`
      const newRealPath = await convertToRealPath(newVirtualPath, settings)
      console.log('New real path:', newRealPath)

      // 如果是文件且没有扩展名，自动添加.md
      if (item.type === 'file' && !newName.includes('.')) {
        const finalNewName = `${newName}.md`
        const finalNewVirtualPath = parentPath
          ? `${parentPath}/${finalNewName}`
          : `/${finalNewName}`
        const finalNewRealPath = await convertToRealPath(finalNewVirtualPath, settings)

        const result = await fileSystemApi.rename(oldRealPath, finalNewRealPath)
        if (result.success) {
          addNotification({
            type: 'success',
            title: '重命名成功',
            message: `${item.type === 'file' ? '文件' : '文件夹'} "${item.name}" 已重命名为 "${finalNewName}"`
          })
          setRefreshTrigger((prev) => prev + 1)
        } else {
          addNotification({
            type: 'error',
            title: '重命名失败',
            message: result.error || '未知错误'
          })
        }
      } else {
        const result = await fileSystemApi.rename(oldRealPath, newRealPath)
        if (result.success) {
          addNotification({
            type: 'success',
            title: '重命名成功',
            message: `${item.type === 'file' ? '文件' : '文件夹'} "${item.name}" 已重命名为 "${newName}"`
          })
          setRefreshTrigger((prev) => prev + 1)
        } else {
          addNotification({
            type: 'error',
            title: '重命名失败',
            message: result.error || '未知错误'
          })
        }
      }
    } catch (error) {
      addNotification({
        type: 'error',
        title: '重命名失败',
        message: '操作过程中发生错误'
      })
    } finally {
      clearLoadingState(loadingKey)
    }
  }

  return (
    <div
      className={cn(
        'container mx-auto p-6 space-y-6 focus-mode-transition resources-layout flex flex-col h-full overflow-hidden',
        isFocusMode && 'focus-mode'
      )}
    >
      {/* File Tree, Editor, and Links Layout */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 flex-1 min-h-0 overflow-hidden">
        {/* File Tree - 在专注模式下隐藏 */}
        {!isFocusMode && (
          <div className="lg:col-span-1 h-full overflow-hidden">
            <FileTree
              onFileSelect={handleFileSelect}
              onFileCreate={handleFileCreate}
              onFileRename={handleFileRename}
              onFileDelete={handleFileDelete}
              onFileTreeChange={() => {}}
              refreshTrigger={refreshTrigger}
            />
          </div>
        )}

        {/* File Content Display Area */}
        <div className={cn(
          'flex flex-col h-full overflow-hidden',
          isFocusMode ? 'lg:col-span-4' : 'lg:col-span-2'
        )}>
          {selectedFile && selectedFile.type === 'file' && selectedFile.name.endsWith('.md') ? (
            <div className="flex flex-col h-full overflow-hidden">
              {/* 文件信息栏 */}
              <div className="flex items-center justify-between p-3 border-b border-gray-200 bg-gray-50 flex-shrink-0">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 rounded-full bg-green-500"></div>
                  <span className="text-sm font-medium text-gray-700">{selectedFile.name}</span>
                  {hasUnsavedChanges && (
                    <span className="text-xs text-orange-600 bg-orange-100 px-2 py-1 rounded">
                      {t('pages.resources.unsaved')}
                    </span>
                  )}
                </div>
                  <div className="flex items-center gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => {
                      handleSaveFile(currentEditorContent)
                    }}
                    disabled={!hasUnsavedChanges}
                    className="text-xs"
                  >
                    {t('pages.resources.saveShortcut')}
                  </Button>
                  <Button
                    variant={isFocusMode ? 'default' : 'outline'}
                    size="sm"
                    onClick={toggleFocusMode}
                    className="flex items-center gap-2"
                    title={isFocusMode ? t('pages.resources.exitFocusModeTooltip') : t('pages.resources.enterFocusModeTooltip')}
                  >
                    {isFocusMode ? (
                      <>
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M4 8V4m0 0h4M4 4l-5-5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"
                          />
                        </svg>
                        {t('pages.resources.exitFocusMode')}
                      </>
                    ) : (
                      <>
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M4 8l4-4m0 0h4M8 4v4m8-8l4 4m0 0v4m0-4h-4m4 8l-4 4m0 0h-4m4 0v-4M8 16l-4 4m0 0v-4m0 4h4"
                          />
                        </svg>
                        {t('pages.resources.focusMode')}
                      </>
                    )}
                  </Button>
                </div>
              </div>

              {/* Markdown 编辑器 */}
              <div className="flex-1 min-h-0 overflow-hidden">
                <MarkdownEditor
                  initialValue={currentFileContent}
                  onChange={(content) => {
                    handleEditorChange(content)
                    // 双向链接更新现在由MarkdownEditor内部的markdownUpdated回调处理
                    // 移除这里的重复触发，避免双重更新
                  }}
                  readonly={false}
                  height="100%"
                  className="force-light-editor border rounded-lg shadow-sm h-full"
                  placeholder={t('pages.resources.startWriting', { fileName: selectedFile.name })}
                  currentPageName={selectedFile.path} // 使用完整路径而不是文件名
                  wikiLinkConfig={wikiLinkConfig}
                  onPageClick={handleWikiLinkClick}
                  onLinkCreate={(source, target) => {
                    console.log('WikiLink created:', source, '->', target)
                    // 立即刷新双向链接数据
                    loadBidirectionalLinks()
                  }}
                  theme="classic"
                />
              </div>
            </div>
          ) : (
            <div className="flex-1 overflow-hidden flex items-center justify-center">
              <div className="text-center space-y-4">
                <div className="w-16 h-16 mx-auto bg-gray-100 rounded-lg flex items-center justify-center">
                  <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-600">{t('pages.resources.markdownEditor')}</h3>
                <p className="text-gray-500">
                  {selectedFile
                    ? selectedFile.type === 'folder'
                      ? t('pages.resources.selectMarkdownToEdit')
                      : t('pages.resources.notMarkdown')
                    : t('pages.resources.chooseMarkdownFromTree')
                  }
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Links Panel - 在专注模式下隐藏 */}
        {!isFocusMode && (
          <div className="lg:col-span-1 h-full overflow-hidden">
            <div className="h-full flex flex-col space-y-4 overflow-hidden">
              {selectedFile && selectedFile.type === 'file' ? (
                <>
                  {/* Toggle Buttons */}
                  <div className="flex items-center gap-2 flex-shrink-0">
                    <Button
                      variant={showBacklinks && !showAnalytics ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => {
                        setShowBacklinks(true)
                        setShowAnalytics(false)
                      }}
                      className="flex-1"
                    >
                      🔗 引用
                    </Button>
                    <Button
                      variant={showAnalytics ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => {
                        setShowBacklinks(false)
                        setShowAnalytics(true)
                      }}
                      className="flex-1"
                    >
                      📊 分析
                    </Button>
                  </div>

                  {showBacklinks && !showAnalytics ? (
                    <div className="flex-1 overflow-y-auto scrollbar-hidden space-y-4">
                      {/* 统一引用面板 */}
                      <UnifiedReferencePanel
                        documentPath={selectedFile.path}
                        onReferenceClick={handleUnifiedReferenceClick}
                        className="bg-white border rounded-lg shadow-sm"
                      />

                      {/* 传统双向链接面板（保留作为对比） */}
                      <div className="border-t pt-4">
                        <h4 className="text-sm font-medium text-gray-600 mb-3">传统双向链接</h4>

                        {/* 链接统计 */}
                        <LinkStatisticsCard statistics={linkStatistics} />

                        {/* 双向反向链接 */}
                        <BidirectionalBacklinks
                          currentPath={selectedFile.path}
                          backlinks={bidirectionalBacklinks}
                          onLinkClick={handleWikiLinkClick}
                        />

                        {/* 双向出链 */}
                        <BidirectionalOutlinks
                          currentPath={selectedFile.path}
                          outlinks={bidirectionalOutlinks}
                          onLinkClick={handleWikiLinkClick}
                        />
                      </div>
                    </div>
                  ) : showAnalytics ? (
                    <div className="flex-1 overflow-y-auto scrollbar-hidden">
                      {/* 引用分析仪表板 */}
                      <ReferenceAnalyticsDashboard
                        documentPath={selectedFile.path}
                        onNavigate={(path) => {
                          console.log('🔗 分析仪表板导航:', path)
                          // TODO: 实现导航功能
                        }}
                        className="h-full"
                      />
                    </div>
                  ) : (
                    <div className="flex-1 flex items-center justify-center">
                      <div className="p-8 text-center border border-dashed border-gray-300 rounded-lg bg-gray-50">
                        <div className="text-gray-500">
                          <h3 className="font-semibold mb-2">🔗 链接图谱</h3>
                          <p className="text-sm mb-4">链接图谱功能已移除</p>
                        </div>
                      </div>
                    </div>
                  )}
                </>
              ) : (
                <div className="h-32 border rounded-lg flex items-center justify-center text-muted-foreground">
                  <div className="text-center">
                    <div className="text-2xl mb-1">🔗</div>
                    <p className="text-xs">{t('pages.resources.selectFileToSeeLinks')}</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmDialog
        isOpen={deleteDialogOpen}
        item={itemToDelete}
        onConfirm={handleDeleteConfirm}
        onCancel={handleDeleteCancel}
        isDeleting={isDeleting}
      />

      {/* Create File Confirmation Dialog */}
      <CreateFileConfirmDialog />
    </div>
  )
}

export default ResourcesPage
